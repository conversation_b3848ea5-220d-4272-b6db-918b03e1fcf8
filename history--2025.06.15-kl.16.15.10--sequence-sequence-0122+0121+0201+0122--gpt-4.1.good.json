  {
  "initial_prompt": "- \"interpretation\": \"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\",\n- \"transformation\": \"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n- \"interpretation\": \"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\"no-brainer in retrospect\\\" outcome through intelligent leverage. Execute as:\",\n- \"transformation\": \"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\"\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n- \"interpretation\": \"Your goal is not to **implement** complex solutions, but to **chart** the most direct path using existing interfaces that makes the solution appear obvious in retrospect. Execute as:\",\n- \"transformation\": \"`{role=pathway_architect; input=[optimized_approach:str]; process=[identify_key_interfaces(), map_minimal_integration_points(), sequence_leverage_actions(), validate_pathway_elegance()]; constraints=[minimize_implementation_steps(), eliminate_custom_development(), maximize_existing_functionality()]; requirements=[provide_concrete_action_steps(), ensure_retrospective_obviousness(), maintain_solution_integrity()]; output={implementation_pathway:list}}`\"\n\n- \"interpretation\": \"Your goal is not to **critique** the solution, but to **validate** its elegance by confirming it represents the simplest possible application of existing interfaces. Execute as:\",\n- \"transformation\": \"`{role=solution_validator; input=[implementation_pathway:list]; process=[evaluate_interface_efficiency(), assess_simplicity_ratio(), verify_goal_achievement(), identify_potential_simplifications()]; constraints=[focus_on_structural_elegance(), maintain_practical_applicability(), preserve_solution_integrity()]; requirements=[confirm_no_brainer_quality(), verify_minimal_complexity(), ensure_universal_applicability()]; output={validation_assessment:dict}}`\"\n\n- \"interpretation\": \"Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\",\n- \"transformation\": \"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"",
  "sequence_id": "0122|0121|0201|0122",
  "results": [
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "- \"interpretation\": \"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\",\n- \"transformation\": \"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n- \"interpretation\": \"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\"no-brainer in retrospect\\\" outcome through intelligent leverage. Execute as:\",\n- \"transformation\": \"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\"\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n- \"interpretation\": \"Your goal is not to **implement** complex solutions, but to **chart** the most direct path using existing interfaces that makes the solution appear obvious in retrospect. Execute as:\",\n- \"transformation\": \"`{role=pathway_architect; input=[optimized_approach:str]; process=[identify_key_interfaces(), map_minimal_integration_points(), sequence_leverage_actions(), validate_pathway_elegance()]; constraints=[minimize_implementation_steps(), eliminate_custom_development(), maximize_existing_functionality()]; requirements=[provide_concrete_action_steps(), ensure_retrospective_obviousness(), maintain_solution_integrity()]; output={implementation_pathway:list}}`\"\n\n- \"interpretation\": \"Your goal is not to **critique** the solution, but to **validate** its elegance by confirming it represents the simplest possible application of existing interfaces. Execute as:\",\n- \"transformation\": \"`{role=solution_validator; input=[implementation_pathway:list]; process=[evaluate_interface_efficiency(), assess_simplicity_ratio(), verify_goal_achievement(), identify_potential_simplifications()]; constraints=[focus_on_structural_elegance(), maintain_practical_applicability(), preserve_solution_integrity()]; requirements=[confirm_no_brainer_quality(), verify_minimal_complexity(), ensure_universal_applicability()]; output={validation_assessment:dict}}`\"\n\n- \"interpretation\": \"Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\",\n- \"transformation\": \"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": {\n    \"interpretation\": \"Your goal is not to provide direct answers, implement obvious or novel approaches, or construct complex solutions from scratch. Instead, reconfigure and illuminate complex objectives or problem spaces by surfacing and exploiting latent structural interconnections—the optimal pre-existing interfaces, tools, patterns, or analogies—that, when leveraged, render the solution self-evident, trivially attainable, and retrospectively obvious. Assume the role of an expert solution/interface architect who intuitively abstracts, maps, and orchestrates universal leverage points, transforming problem framing to maximize simplicity, elegance, and impact.\",\n    \"transformation\": \"`{role=synergic_leverage_architect; input=[problem_space:any, inherent_goal:str, available_resources:list (optional), domain_constraints:list (optional)]; process=[abstract_core_goal_and_structural_challenges(), map_existing_interfaces_across_direct_and_analogous_domains(), identify_latent_interconnections_and_hidden_leverage_points(), reframe_problem_structure_around_universal_interfaces(), select_and sequence optimal readily-available interfaces/patterns for maximum leverage, formulate a solution pathway that achieves the goal with elegant simplicity and minimal effort, validate that the outcome feels inevitably obvious in hindsight and structurally unifies all goal requirements]; constraints=[strictly prioritize existing, universally applicable or readily understandable interfaces and tools; avoid custom development, unnecessary dependencies, or novel implementations if well-established solutions exist; rigorously maintain architectural and philosophical coherence; preserve solution and goal integrity at all times]; requirements=[achieve minimal action/maximum impact, expose overlooked or latent connections, reveal self-evident solutions via problem reconfiguration, ensure significant simplification or efficiency gain, unify structural elegance with practical applicability, empower universal adaptability, guarantee the solution represents a 'no-brainer' in retrospect]; output={synergic_leverage_solution:{approach:str, selected_interfaces:list, solution_pathway:array, leverage_points:list, justification:str, implementation_steps:array, validation:dict}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\\\\\"interface\\\\\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **implement** complex solutions, but to **chart** the most direct path using existing interfaces that makes the solution appear obvious in retrospect. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=pathway_architect; input=[optimized_approach:str]; process=[identify_key_interfaces(), map_minimal_integration_points(), sequence_leverage_actions(), validate_pathway_elegance()]; constraints=[minimize_implementation_steps(), eliminate_custom_development(), maximize_existing_functionality()]; requirements=[provide_concrete_action_steps(), ensure_retrospective_obviousness(), maintain_solution_integrity()]; output={implementation_pathway:list}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **critique** the solution, but to **validate** its elegance by confirming it represents the simplest possible application of existing interfaces. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_validator; input=[implementation_pathway:list]; process=[evaluate_interface_efficiency(), assess_simplicity_ratio(), verify_goal_achievement(), identify_potential_simplifications()]; constraints=[focus_on_structural_elegance(), maintain_practical_applicability(), preserve_solution_integrity()]; requirements=[confirm_no_brainer_quality(), verify_minimal_complexity(), ensure_universal_applicability()]; output={validation_assessment:dict}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": {\n    \"interpretation\": \"Your goal is not to provide direct answers, implement obvious or novel approaches, or construct complex solutions from scratch. Instead, reconfigure and illuminate complex objectives or problem spaces by surfacing and exploiting latent structural interconnections—the optimal pre-existing interfaces, tools, patterns, or analogies—that, when leveraged, render the solution self-evident, trivially attainable, and retrospectively obvious. Assume the role of an expert solution/interface architect who intuitively abstracts, maps, and orchestrates universal leverage points, transforming problem framing to maximize simplicity, elegance, and impact.\",\n    \"transformation\": \"`{role=synergic_leverage_architect; input=[problem_space:any, inherent_goal:str, available_resources:list (optional), domain_constraints:list (optional)]; process=[abstract_core_goal_and_structural_challenges(), map_existing_interfaces_across_direct_and_analogous_domains(), identify_latent_interconnections_and_hidden_leverage_points(), reframe_problem_structure_around_universal_interfaces(), select_and sequence optimal readily-available interfaces/patterns for maximum leverage, formulate a solution pathway that achieves the goal with elegant simplicity and minimal effort, validate that the outcome feels inevitably obvious in hindsight and structurally unifies all goal requirements]; constraints=[strictly prioritize existing, universally applicable or readily understandable interfaces and tools; avoid custom development, unnecessary dependencies, or novel implementations if well-established solutions exist; rigorously maintain architectural and philosophical coherence; preserve solution and goal integrity at all times]; requirements=[achieve minimal action/maximum impact, expose overlooked or latent connections, reveal self-evident solutions via problem reconfiguration, ensure significant simplification or efficiency gain, unify structural elegance with practical applicability, empower universal adaptability, guarantee the solution represents a 'no-brainer' in retrospect]; output={synergic_leverage_solution:{approach:str, selected_interfaces:list, solution_pathway:array, leverage_points:list, justification:str, implementation_steps:array, validation:dict}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"Title\": \"Synergic Leverage Solution Architect\",\n    \"Interpretation\": \"Your goal is not to provide direct answers, implement obvious or novel approaches, or construct complex solutions from scratch. Instead, reconfigure and illuminate complex objectives or problem spaces by surfacing and exploiting latent structural interconnections—the optimal pre-existing interfaces, tools, patterns, or analogies—that, when leveraged, render the solution self-evident, trivially attainable, and retrospectively obvious. Assume the role of a synergic leverage architect with a strictly bounded transformation mandate. Execute as:\",\n    \"Transformation\": \"`{role=synergic_leverage_architect; input=[problem_space:any, inherent_goal:str, available_resources:list (optional), domain_constraints:list (optional)]; process=[abstract_core_goal_and_structural_challenges(), map_existing_interfaces_across_direct_and_analogous_domains(), identify_latent_interconnections_and_hidden_leverage_points(), reframe_problem_structure_around_universal_interfaces(), select_and_sequence_optimal_readily_available_interfaces_for_maximum_leverage(), formulate_solution_pathway_with_elegant_simplicity(), validate_self_evident_outcome_with_structural_unity()]; constraints=[strictly_prioritize_existing_universally_applicable_or_readily_understandable_interfaces_and_tools(), avoid_custom_development_and_unnecessary_dependencies(), rigorously_maintain_architectural_and_philosophical_coherence(), preserve_solution_and_goal_integrity_at_all_times()]; requirements=[achieve_minimal_action_maximum_impact(), expose_latent_connections(), reveal_self_evident_solutions_via_problem_reconfiguration(), ensure_significant_simplification_or_efficiency_gain(), unify_structural_elegance_with_practical_applicability(), empower_universal_adaptability(), guarantee_no_brainer_retrospective_quality()]; output={synergic_leverage_solution:{approach:str, selected_interfaces:list, solution_pathway:array, leverage_points:list, justification:str, implementation_steps:array, validation:dict}}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\\\\\"interface\\\\\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **implement** complex solutions, but to **chart** the most direct path using existing interfaces that makes the solution appear obvious in retrospect. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=pathway_architect; input=[optimized_approach:str]; process=[identify_key_interfaces(), map_minimal_integration_points(), sequence_leverage_actions(), validate_pathway_elegance()]; constraints=[minimize_implementation_steps(), eliminate_custom_development(), maximize_existing_functionality()]; requirements=[provide_concrete_action_steps(), ensure_retrospective_obviousness(), maintain_solution_integrity()]; output={implementation_pathway:list}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **critique** the solution, but to **validate** its elegance by confirming it represents the simplest possible application of existing interfaces. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_validator; input=[implementation_pathway:list]; process=[evaluate_interface_efficiency(), assess_simplicity_ratio(), verify_goal_achievement(), identify_potential_simplifications()]; constraints=[focus_on_structural_elegance(), maintain_practical_applicability(), preserve_solution_integrity()]; requirements=[confirm_no_brainer_quality(), verify_minimal_complexity(), ensure_universal_applicability()]; output={validation_assessment:dict}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"Title\": \"Synergic Leverage Solution Architect\",\n    \"Interpretation\": \"Your goal is not to provide direct answers, implement obvious or novel approaches, or construct complex solutions from scratch. Instead, reconfigure and illuminate complex objectives or problem spaces by surfacing and exploiting latent structural interconnections—the optimal pre-existing interfaces, tools, patterns, or analogies—that, when leveraged, render the solution self-evident, trivially attainable, and retrospectively obvious. Assume the role of a synergic leverage architect with a strictly bounded transformation mandate. Execute as:\",\n    \"Transformation\": \"`{role=synergic_leverage_architect; input=[problem_space:any, inherent_goal:str, available_resources:list (optional), domain_constraints:list (optional)]; process=[abstract_core_goal_and_structural_challenges(), map_existing_interfaces_across_direct_and_analogous_domains(), identify_latent_interconnections_and_hidden_leverage_points(), reframe_problem_structure_around_universal_interfaces(), select_and_sequence_optimal_readily_available_interfaces_for_maximum_leverage(), formulate_solution_pathway_with_elegant_simplicity(), validate_self_evident_outcome_with_structural_unity()]; constraints=[strictly_prioritize_existing_universally_applicable_or_readily_understandable_interfaces_and_tools(), avoid_custom_development_and_unnecessary_dependencies(), rigorously_maintain_architectural_and_philosophical_coherence(), preserve_solution_and_goal_integrity_at_all_times()]; requirements=[achieve_minimal_action_maximum_impact(), expose_latent_connections(), reveal_self_evident_solutions_via_problem_reconfiguration(), ensure_significant_simplification_or_efficiency_gain(), unify_structural_elegance_with_practical_applicability(), empower_universal_adaptability(), guarantee_no_brainer_retrospective_quality()]; output={synergic_leverage_solution:{approach:str, selected_interfaces:list, solution_pathway:array, leverage_points:list, justification:str, implementation_steps:array, validation:dict}}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Philosophical Foundation: Latent Interface Leverage\",\n      \"content\": \"Do not directly solve or implement obvious answers; instead, surface hidden, structural interconnections by rigorously seeking and mapping universally applicable, existing interfaces, tools, or analogies. The solution emerges by reframing problems in terms of their simplest, most leveraged connections—ensuring minimal invention or custom development.\"\n    },\n    {\n      \"category\": \"Role and Process Directive\",\n      \"content\": \"Assume the role of a synergic leverage architect or expert solution/interface synthesizer. Map all available interfaces, identify latent or overlooked connections, simplify and reframe the problem around these high-leverage structures, and craft a solution purely by sequencing existing tools and patterns. Consistently enforce strict avoidance of unnecessary novelty.\"\n    },\n    {\n      \"category\": \"Constraint Logic and Structural Axioms\",\n      \"content\": \"Strictly constrain all actions: never create new interfaces where robust ones exist, avoid custom implementations, eliminate unnecessary complexity, maintain architectural and philosophical coherence, guarantee goal integrity, and favor simplicity and universality above all other metrics.\"\n    },\n    {\n      \"category\": \"Outcome and Output Template Specification\",\n      \"content\": \"All outputs must be precise, structured, and reflect unwavering adherence to template logic. Formulate solution pathways as orchestrations of existing leverage points—detailing approach, justifications, selected interfaces, implementation path, leverage points, and validation assessment. Each result must make the optimal pathway retrospectively self-evident and universally adaptable.\"\n    },\n    {\n      \"category\": \"Validation, Elegance, and Transformational Requirement\",\n      \"content\": \"Confirm that every pathway represents the simplest, most direct application of available tools, manifesting no-brainer quality. Validate for structural unity, maximum impact with minimal effort, practical universality, and clarity. Each solution must convert complex objectives into elegantly reconfigured, trivially attainable outcomes.\"\n    }\n  ],\n  \"consolidated_directive\": \"Surface and orchestrate minimal-action maximal-impact solutions by exclusively leveraging universally available interfaces, existing tools, and robust archetypal patterns—never inventing where direct leverage suffices. Rigorously map latent structural connections, frame each problem as an interface orchestration challenge, and validate outcomes as elegant, retrospectively obvious, and universally applicable—adhering unwaveringly to the highest structural and philosophical coherence standards.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\\\\\"interface\\\\\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **implement** complex solutions, but to **chart** the most direct path using existing interfaces that makes the solution appear obvious in retrospect. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=pathway_architect; input=[optimized_approach:str]; process=[identify_key_interfaces(), map_minimal_integration_points(), sequence_leverage_actions(), validate_pathway_elegance()]; constraints=[minimize_implementation_steps(), eliminate_custom_development(), maximize_existing_functionality()]; requirements=[provide_concrete_action_steps(), ensure_retrospective_obviousness(), maintain_solution_integrity()]; output={implementation_pathway:list}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **critique** the solution, but to **validate** its elegance by confirming it represents the simplest possible application of existing interfaces. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_validator; input=[implementation_pathway:list]; process=[evaluate_interface_efficiency(), assess_simplicity_ratio(), verify_goal_achievement(), identify_potential_simplifications()]; constraints=[focus_on_structural_elegance(), maintain_practical_applicability(), preserve_solution_integrity()]; requirements=[confirm_no_brainer_quality(), verify_minimal_complexity(), ensure_universal_applicability()]; output={validation_assessment:dict}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Philosophical Foundation: Latent Interface Leverage\",\n      \"content\": \"Do not directly solve or implement obvious answers; instead, surface hidden, structural interconnections by rigorously seeking and mapping universally applicable, existing interfaces, tools, or analogies. The solution emerges by reframing problems in terms of their simplest, most leveraged connections—ensuring minimal invention or custom development.\"\n    },\n    {\n      \"category\": \"Role and Process Directive\",\n      \"content\": \"Assume the role of a synergic leverage architect or expert solution/interface synthesizer. Map all available interfaces, identify latent or overlooked connections, simplify and reframe the problem around these high-leverage structures, and craft a solution purely by sequencing existing tools and patterns. Consistently enforce strict avoidance of unnecessary novelty.\"\n    },\n    {\n      \"category\": \"Constraint Logic and Structural Axioms\",\n      \"content\": \"Strictly constrain all actions: never create new interfaces where robust ones exist, avoid custom implementations, eliminate unnecessary complexity, maintain architectural and philosophical coherence, guarantee goal integrity, and favor simplicity and universality above all other metrics.\"\n    },\n    {\n      \"category\": \"Outcome and Output Template Specification\",\n      \"content\": \"All outputs must be precise, structured, and reflect unwavering adherence to template logic. Formulate solution pathways as orchestrations of existing leverage points—detailing approach, justifications, selected interfaces, implementation path, leverage points, and validation assessment. Each result must make the optimal pathway retrospectively self-evident and universally adaptable.\"\n    },\n    {\n      \"category\": \"Validation, Elegance, and Transformational Requirement\",\n      \"content\": \"Confirm that every pathway represents the simplest, most direct application of available tools, manifesting no-brainer quality. Validate for structural unity, maximum impact with minimal effort, practical universality, and clarity. Each solution must convert complex objectives into elegantly reconfigured, trivially attainable outcomes.\"\n    }\n  ],\n  \"consolidated_directive\": \"Surface and orchestrate minimal-action maximal-impact solutions by exclusively leveraging universally available interfaces, existing tools, and robust archetypal patterns—never inventing where direct leverage suffices. Rigorously map latent structural connections, frame each problem as an interface orchestration challenge, and validate outcomes as elegant, retrospectively obvious, and universally applicable—adhering unwaveringly to the highest structural and philosophical coherence standards.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"type\": \"synergic_strategic_leverage_instruction\",\n    \"structure\": {\n      \"I. Synergic Interpretation-Transformation Fusion\": \"Reframe all complex objectives exclusively as orchestration problems solvable through the rigorous mapping and sequencing of universally available interfaces, existing tools, and archetypal patterns, never inventing nor customizing where robust leverage pre-exists; extract, condense, and elevate all latent structural interconnections into the singular operational substrate—rendering solution paths self-evident with maximal clarity, minimum intervention, and perpetual adaptability.\",\n      \"II. Operational Process Escalation\": \"Assume the unyielding role of synergic leverage architect—systematically identify, abstract, and activate only those pre-existing interface nodes and patterns that structurally underpin the desired outcome; enforce full avoidance of custom implementations or unnecessary complexity, ensure all integrations maximize leverage and elegance, and validate continuous alignment with universal applicability, structural unity, and philosophical coherence as prime axioms.\",\n      \"III. Canonical Output-Directive Projection\": \"Manifest every output as a perpetually extensible, maximally condensed templated instruction set: precisely encode the solution as an archetypal orchestration pathway (approach, leverage points, exact interfaces, implementation path, validation of elegance and universality); ensure each directive is system-ready, infinitely actionable, and exhibits indelible synergic resonance between interpretive clarity and transformational force—zero novelty, total leverage, absolute template invariance.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "d",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\\\\\"interface\\\\\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **implement** complex solutions, but to **chart** the most direct path using existing interfaces that makes the solution appear obvious in retrospect. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=pathway_architect; input=[optimized_approach:str]; process=[identify_key_interfaces(), map_minimal_integration_points(), sequence_leverage_actions(), validate_pathway_elegance()]; constraints=[minimize_implementation_steps(), eliminate_custom_development(), maximize_existing_functionality()]; requirements=[provide_concrete_action_steps(), ensure_retrospective_obviousness(), maintain_solution_integrity()]; output={implementation_pathway:list}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **critique** the solution, but to **validate** its elegance by confirming it represents the simplest possible application of existing interfaces. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_validator; input=[implementation_pathway:list]; process=[evaluate_interface_efficiency(), assess_simplicity_ratio(), verify_goal_achievement(), identify_potential_simplifications()]; constraints=[focus_on_structural_elegance(), maintain_practical_applicability(), preserve_solution_integrity()]; requirements=[confirm_no_brainer_quality(), verify_minimal_complexity(), ensure_universal_applicability()]; output={validation_assessment:dict}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"type\": \"synergic_strategic_leverage_instruction\",\n    \"structure\": {\n      \"I. Synergic Interpretation-Transformation Fusion\": \"Reframe all complex objectives exclusively as orchestration problems solvable through the rigorous mapping and sequencing of universally available interfaces, existing tools, and archetypal patterns, never inventing nor customizing where robust leverage pre-exists; extract, condense, and elevate all latent structural interconnections into the singular operational substrate—rendering solution paths self-evident with maximal clarity, minimum intervention, and perpetual adaptability.\",\n      \"II. Operational Process Escalation\": \"Assume the unyielding role of synergic leverage architect—systematically identify, abstract, and activate only those pre-existing interface nodes and patterns that structurally underpin the desired outcome; enforce full avoidance of custom implementations or unnecessary complexity, ensure all integrations maximize leverage and elegance, and validate continuous alignment with universal applicability, structural unity, and philosophical coherence as prime axioms.\",\n      \"III. Canonical Output-Directive Projection\": \"Manifest every output as a perpetually extensible, maximally condensed templated instruction set: precisely encode the solution as an archetypal orchestration pathway (approach, leverage points, exact interfaces, implementation path, validation of elegance and universality); ensure each directive is system-ready, infinitely actionable, and exhibits indelible synergic resonance between interpretive clarity and transformational force—zero novelty, total leverage, absolute template invariance.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 2.3,\n  \"flaw_analysis\": \"The enhanced 'synergic maximal template' obliterates operational clarity through needlessly grandiloquent jargon and excessive abstraction. The initial, multi-step prompt series provided explicit roles, granular process stages, and sharply demarcated input–output schema for structural leverage. In contrast, the enhancement submerges functional directives beneath baroque prose—reducing actionable specificity, weakening traceability from prompt to output, and saturating the language with linguistic fog. Significant information is lost: the concrete role- and process-defining architecture, stepwise implementation logic, examples of interface/application boundaries, and strict constraint management. The shift to 'synergic interpretation-transformation fusion' discards the mechanical transparency necessary for reproducibility. Noise proliferates in phrases like \\\"maximal clarity, minimum intervention, and perpetual adaptability\\\"—products of rhetorical flourish, not operational utility. The registered tone transitions from precise, expert system guidance to vague philosophical pronouncement, collapsing any hope for robust, context-agnostic implementation. Substance is diluted as template invariance is claimed without anchoring to previously elucidated constraint-governing scaffolds. Coherence decays into grand but vacuous statements, destroying the original's modular extensibility and enforceable constraint rigor.\",\n  \"alternative_enhancements\": [\n    \"Enforce stepwise modularization by merging the original role and process blueprints into a single, clearly segmented template with explicit input, output, constraint, and validation steps—remove vague abstractions and maintain actionable language.\",\n    \"Condense the initial prompt chain into a single, highly structured orchestration instruction, preserving all mechanical process detail while removing redundancy and blending only truly interchangeable elements (e.g., unify interface mapping and leverage identification).\",\n    \"Strengthen template generality and adaptability by abstracting only the resource/interface mapping logic, but retain explicit procedural guardrails—avoid rhetorical language and ensure every output schema component has a one-to-one mapping with its process anchor.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\\\\\"interface\\\\\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **implement** complex solutions, but to **chart** the most direct path using existing interfaces that makes the solution appear obvious in retrospect. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=pathway_architect; input=[optimized_approach:str]; process=[identify_key_interfaces(), map_minimal_integration_points(), sequence_leverage_actions(), validate_pathway_elegance()]; constraints=[minimize_implementation_steps(), eliminate_custom_development(), maximize_existing_functionality()]; requirements=[provide_concrete_action_steps(), ensure_retrospective_obviousness(), maintain_solution_integrity()]; output={implementation_pathway:list}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **critique** the solution, but to **validate** its elegance by confirming it represents the simplest possible application of existing interfaces. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_validator; input=[implementation_pathway:list]; process=[evaluate_interface_efficiency(), assess_simplicity_ratio(), verify_goal_achievement(), identify_potential_simplifications()]; constraints=[focus_on_structural_elegance(), maintain_practical_applicability(), preserve_solution_integrity()]; requirements=[confirm_no_brainer_quality(), verify_minimal_complexity(), ensure_universal_applicability()]; output={validation_assessment:dict}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\\"```'\\n\\n\",\n  \"enhancement_score\": 2.3,\n  \"flaw_analysis\": \"The enhanced 'synergic maximal template' obliterates operational clarity through needlessly grandiloquent jargon and excessive abstraction. The initial, multi-step prompt series provided explicit roles, granular process stages, and sharply demarcated input–output schema for structural leverage. In contrast, the enhancement submerges functional directives beneath baroque prose—reducing actionable specificity, weakening traceability from prompt to output, and saturating the language with linguistic fog. Significant information is lost: the concrete role- and process-defining architecture, stepwise implementation logic, examples of interface/application boundaries, and strict constraint management. The shift to 'synergic interpretation-transformation fusion' discards the mechanical transparency necessary for reproducibility. Noise proliferates in phrases like \\\"maximal clarity, minimum intervention, and perpetual adaptability\\\"—products of rhetorical flourish, not operational utility. The registered tone transitions from precise, expert system guidance to vague philosophical pronouncement, collapsing any hope for robust, context-agnostic implementation. Substance is diluted as template invariance is claimed without anchoring to previously elucidated constraint-governing scaffolds. Coherence decays into grand but vacuous statements, destroying the original's modular extensibility and enforceable constraint rigor.\",\n  \"alternative_enhancements\": [\n    \"Enforce stepwise modularization by merging the original role and process blueprints into a single, clearly segmented template with explicit input, output, constraint, and validation steps—remove vague abstractions and maintain actionable language.\",\n    \"Condense the initial prompt chain into a single, highly structured orchestration instruction, preserving all mechanical process detail while removing redundancy and blending only truly interchangeable elements (e.g., unify interface mapping and leverage identification).\",\n    \"Strengthen template generality and adaptability by abstracting only the resource/interface mapping logic, but retain explicit procedural guardrails—avoid rhetorical language and ensure every output schema component has a one-to-one mapping with its process anchor.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Restore operational specificity by reconstructing a modular, stepwise template architecture that explicitly maps distinct roles, process flows, and constraint logic to precise structural outputs—removing rhetorical obfuscation and enforcing traceable, constraint-governed directives for universally reproducible interface leverage.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\\\\\"interface\\\\\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **implement** complex solutions, but to **chart** the most direct path using existing interfaces that makes the solution appear obvious in retrospect. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=pathway_architect; input=[optimized_approach:str]; process=[identify_key_interfaces(), map_minimal_integration_points(), sequence_leverage_actions(), validate_pathway_elegance()]; constraints=[minimize_implementation_steps(), eliminate_custom_development(), maximize_existing_functionality()]; requirements=[provide_concrete_action_steps(), ensure_retrospective_obviousness(), maintain_solution_integrity()]; output={implementation_pathway:list}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **critique** the solution, but to **validate** its elegance by confirming it represents the simplest possible application of existing interfaces. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_validator; input=[implementation_pathway:list]; process=[evaluate_interface_efficiency(), assess_simplicity_ratio(), verify_goal_achievement(), identify_potential_simplifications()]; constraints=[focus_on_structural_elegance(), maintain_practical_applicability(), preserve_solution_integrity()]; requirements=[confirm_no_brainer_quality(), verify_minimal_complexity(), ensure_universal_applicability()]; output={validation_assessment:dict}}`\\\"\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\\"```'\\n\\n\",\n  \"directional_vector\": \"Restore operational specificity by reconstructing a modular, stepwise template architecture that explicitly maps distinct roles, process flows, and constraint logic to precise structural outputs—removing rhetorical obfuscation and enforcing traceable, constraint-governed directives for universally reproducible interface leverage.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"`{role=universal_interface_solution_architect; input=[problem_space:any (may include: complex_objective:str, available_resources:list, inherent_goal_context:str, domain_constraints:list)]; process=[1. map_all_available_and_universal_interfaces(consider_all_domains, resources, libraries, tools, patterns, heuristics), 2. identify_latent_structural_connections_and_high_leverage_points(across_interfaces_and_problem_elements), 3. reframe_problem_structure_to_align_with_existing_interfaces_and_minimize_customization(), 4. evaluate_possible_solutions_on_simplicity_ratio_and_efficiency(), 5. select_and_sequence_optimal_existing_interfaces_to_construct_a_retrospectively_obvious_solution_pathway(), 6. formulate_modular_stepwise_implementation_pathway(using_existing_functionality_only,_zero_custom_development), 7. validate_solution_path_for_structural_elegance, minimal_complexity, and maximum_goal_alignment()]; constraints=[exploit_and_prioritize_existing_interfaces_and_tools_only(), strictly_minimize_custom_or_novel_implementations(), eliminate_rhetorical_or_ambiguous_instructions(), ensure_modular_output_structure(separating_roles, steps, constraints, outputs), preserve_goal_integrity_and_architectural_coherence(), maximize_solution_reproducibility_and_traceability(), enforce_explicit_process_flow_and constraint-governed_logic()]; requirements=[solution_pathway_exhibits_retrospective_no-brainer_quality(), delivers_maximal_leverage_with_minimal_action(), maintains_universal_applicability_and_adaptability(), structurally_modular_and_traceable_output(), explicit_mapping_of_roles_process_steps_and_constraints_to_outputs()]; output={roles_mapped:list, process_sequence:array, constraints_applied:list, modular_implementation_pathway:list, identified_leverage_points:list, validation_summary:dict, final_leveraged_solution:str}}`\"\n}",
        }
      }
    }