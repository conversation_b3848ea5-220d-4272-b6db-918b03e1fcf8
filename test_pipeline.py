#!/usr/bin/env python3
"""
Test Suite for LLM Instruction Pipeline

This test suite validates the deterministic behavior, error handling,
and evaluation accuracy of the LLM instruction pipeline.
"""

import unittest
import json
import tempfile
import os
from pathlib import Path
from llm_instruction_pipeline import LL<PERSON><PERSON>ruction<PERSON><PERSON>eline, InstructionSequence, EvaluationMetrics

class TestLLMInstructionPipeline(unittest.TestCase):
    """Test cases for the LLM Instruction Pipeline"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.pipeline = LLMInstructionPipeline(self.test_dir)
        
        # Create sample test files
        self.create_test_files()
    
    def tearDown(self):
        """Clean up test environment"""
        import shutil
        shutil.rmtree(self.test_dir)
    
    def create_test_files(self):
        """Create sample JSON files for testing"""
        
        # Valid test file
        valid_data = {
            "initial_prompt": "Test prompt for evaluation",
            "sequence_id": "test-1400",
            "results": [
                {
                    "instruction": "# Test Instruction\n\nYour goal is not to **summarize** the input, but to **extract** key information. Execute as:\n\n`{role=test_extractor; input=[text:str]; process=[analyze_content(), extract_key_points()]; constraints=[maintain_accuracy()]; requirements=[structured_output()]; output={extracted_data:dict}}`",
                    "step": "a",
                    "title": "Test Extractor",
                    "input": "Sample input text",
                    "responses": {
                        "gpt-4.1": {
                            "model": "gpt-4.1",
                            "content": "{\"extracted_data\": {\"key_points\": [\"point1\", \"point2\"], \"summary\": \"test summary\"}}"
                        }
                    }
                }
            ]
        }
        
        valid_file = Path(self.test_dir) / "history--2025.06.18--sequence-test-1400--gpt-4.1.good.json"
        with open(valid_file, 'w') as f:
            json.dump(valid_data, f)
        
        # Invalid test file (missing required fields)
        invalid_data = {
            "sequence_id": "test-1401",
            "results": []
        }
        
        invalid_file = Path(self.test_dir) / "history--2025.06.18--sequence-test-1401--gpt-4.1.bad.json"
        with open(invalid_file, 'w') as f:
            json.dump(invalid_data, f)
        
        # Non-matching filename
        non_matching_file = Path(self.test_dir) / "other_file.json"
        with open(non_matching_file, 'w') as f:
            json.dump(valid_data, f)
    
    def test_file_discovery(self):
        """Test file discovery with selection criteria"""
        files = self.pipeline.discover_files()
        
        # Should find only the valid matching file
        self.assertEqual(len(files), 2)  # valid and invalid files match pattern
        
        # Check that non-matching file is excluded
        file_names = [f.name for f in files]
        self.assertNotIn("other_file.json", file_names)
    
    def test_schema_validation(self):
        """Test schema validation functionality"""
        valid_data = {
            "initial_prompt": "test",
            "sequence_id": "test",
            "results": []
        }
        
        invalid_data = {
            "sequence_id": "test"
        }
        
        self.assertTrue(self.pipeline.validate_schema(valid_data, "test.json"))
        self.assertFalse(self.pipeline.validate_schema(invalid_data, "test.json"))
    
    def test_instruction_extraction(self):
        """Test instruction sequence extraction"""
        files = self.pipeline.discover_files()
        sequences = self.pipeline.extract_instruction_sequences(files)
        
        # Should extract at least one sequence from valid file
        self.assertGreater(len(sequences), 0)
        
        # Check sequence structure
        sequence = sequences[0]
        self.assertIsInstance(sequence, InstructionSequence)
        self.assertIsNotNone(sequence.instruction_content)
        self.assertIsNotNone(sequence.response_content)
    
    def test_evaluation_metrics(self):
        """Test evaluation metric calculations"""
        # Create a test sequence with good instruction
        test_sequence = InstructionSequence(
            file_path="test.json",
            sequence_id="test",
            initial_prompt="test prompt",
            instruction_title="Test Instruction",
            instruction_content="# Test\n\nYour goal is not to **summarize** but to **extract**. Execute as:\n\n`{role=extractor; input=[text:str]; process=[analyze()]; constraints=[accuracy()]; requirements=[structured()]; output={data:dict}}`",
            step="a",
            model="gpt-4.1",
            response_content='{"result": "test"}',
            extraction_timestamp="2025-06-18T12:00:00",
            content_hash="test_hash"
        )
        
        metrics = self.pipeline.evaluate_instruction_sequence(test_sequence)
        
        self.assertIsInstance(metrics, EvaluationMetrics)
        self.assertGreaterEqual(metrics.clarity_score, 0.0)
        self.assertLessEqual(metrics.clarity_score, 1.0)
        self.assertGreaterEqual(metrics.overall_comprehension, 0.0)
        self.assertLessEqual(metrics.overall_comprehension, 1.0)
    
    def test_clarity_score_calculation(self):
        """Test clarity score calculation"""
        # High clarity instruction
        high_clarity = "Your goal is not to **summarize** but to **extract**. Execute as: `{role=extractor; input=[text:str]; process=[analyze()]; constraints=[accuracy()]; output={data:dict}}`"
        
        # Low clarity instruction
        low_clarity = "Please analyze the text and provide output."
        
        high_score = self.pipeline._calculate_clarity_score(high_clarity)
        low_score = self.pipeline._calculate_clarity_score(low_clarity)
        
        self.assertGreater(high_score, low_score)
        self.assertGreaterEqual(high_score, 0.8)  # Should be high
        self.assertLessEqual(low_score, 0.3)      # Should be low
    
    def test_operational_specificity(self):
        """Test operational specificity calculation"""
        # Specific instruction
        specific = "Extract [data:str] from input, analyze content(), validate results(), generate output()"
        
        # Vague instruction
        vague = "Process the information"
        
        specific_score = self.pipeline._calculate_operational_specificity(specific)
        vague_score = self.pipeline._calculate_operational_specificity(vague)
        
        self.assertGreater(specific_score, vague_score)
    
    def test_schema_compliance_calculation(self):
        """Test schema compliance calculation"""
        # Compliant instruction
        compliant = "Your goal is not to **summarize** but to **extract**. Execute as: `{role=extractor}`"
        
        # Non-compliant instruction
        non_compliant = "Please analyze the text"
        
        compliant_score = self.pipeline._calculate_schema_compliance(compliant)
        non_compliant_score = self.pipeline._calculate_schema_compliance(non_compliant)
        
        self.assertGreater(compliant_score, non_compliant_score)
    
    def test_semantic_coherence(self):
        """Test semantic coherence calculation"""
        instruction = "Extract data and return JSON"
        
        # Coherent response
        coherent_response = '{"data": "extracted_value"}'
        
        # Incoherent response
        incoherent_response = "This is not related"
        
        coherent_score = self.pipeline._calculate_semantic_coherence(instruction, coherent_response)
        incoherent_score = self.pipeline._calculate_semantic_coherence(instruction, incoherent_response)
        
        self.assertGreater(coherent_score, incoherent_score)
    
    def test_checkpoint_creation(self):
        """Test checkpoint creation and tracking"""
        initial_count = len(self.pipeline.checkpoints)
        
        self.pipeline.create_checkpoint("test_stage", "started", 5, 1, {"detail": "test"})
        
        self.assertEqual(len(self.pipeline.checkpoints), initial_count + 1)
        
        checkpoint = self.pipeline.checkpoints[-1]
        self.assertEqual(checkpoint.stage, "test_stage")
        self.assertEqual(checkpoint.status, "started")
        self.assertEqual(checkpoint.processed_count, 5)
        self.assertEqual(checkpoint.error_count, 1)
    
    def test_report_generation(self):
        """Test comprehensive report generation"""
        # Run extraction and evaluation first
        files = self.pipeline.discover_files()
        sequences = self.pipeline.extract_instruction_sequences(files)
        evaluations = self.pipeline.evaluate_all_sequences()
        
        # Generate report
        report = self.pipeline.generate_comprehensive_report()
        
        self.assertIn("pipeline_metadata", report)
        self.assertIn("aggregate_statistics", report)
        self.assertIn("detailed_analysis", report)
        self.assertIn("execution_checkpoints", report)
        self.assertIn("standards_compliance", report)
        
        # Check aggregate statistics
        stats = report["aggregate_statistics"]
        self.assertIn("total_sequences", stats)
        self.assertIn("overall_system_comprehension", stats)
    
    def test_deterministic_behavior(self):
        """Test that pipeline produces deterministic results"""
        # Run pipeline twice
        files1 = self.pipeline.discover_files()
        sequences1 = self.pipeline.extract_instruction_sequences(files1)
        
        files2 = self.pipeline.discover_files()
        sequences2 = self.pipeline.extract_instruction_sequences(files2)
        
        # Results should be identical
        self.assertEqual(len(sequences1), len(sequences2))
        
        # Content hashes should match
        hashes1 = [seq.content_hash for seq in sequences1]
        hashes2 = [seq.content_hash for seq in sequences2]
        self.assertEqual(sorted(hashes1), sorted(hashes2))
    
    def test_error_handling(self):
        """Test error handling and recovery"""
        # Create a corrupted JSON file
        corrupted_file = Path(self.test_dir) / "history--2025.06.18--sequence-corrupted--gpt-4.1.json"
        with open(corrupted_file, 'w') as f:
            f.write("invalid json content {")
        
        # Pipeline should handle the error gracefully
        files = self.pipeline.discover_files()
        sequences = self.pipeline.extract_instruction_sequences(files)
        
        # Should still process valid files
        self.assertGreater(len(sequences), 0)
        
        # Should have recorded errors in checkpoints
        extraction_checkpoint = next(
            (cp for cp in self.pipeline.checkpoints if cp.stage == "extraction"), 
            None
        )
        self.assertIsNotNone(extraction_checkpoint)
        self.assertGreater(extraction_checkpoint.error_count, 0)


def run_integration_test():
    """Run integration test with actual workspace files"""
    print("Running integration test with actual workspace...")
    
    pipeline = LLMInstructionPipeline(".")
    
    try:
        report = pipeline.execute_pipeline()
        
        print(f"✅ Integration test passed!")
        print(f"📊 Processed {report['aggregate_statistics']['total_sequences']} sequences")
        print(f"📈 Overall comprehension: {report['aggregate_statistics']['overall_system_comprehension']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


if __name__ == "__main__":
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*50)
    
    # Run integration test
    run_integration_test()
