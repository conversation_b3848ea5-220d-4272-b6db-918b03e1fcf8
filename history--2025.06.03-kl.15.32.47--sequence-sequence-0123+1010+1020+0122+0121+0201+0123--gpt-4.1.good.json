  {
  "initial_prompt": "*one, single, maximally enhanced* instruction:\n\n[TEMPLATE_START]\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\n\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\n\nAgent Name: [FILENAME]\nAgent Role: Prompt Optimizer\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\n\nInstructions:\n1. Constants:\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\n   - [ADDITIONAL_CONSTANTS]\n\n2. Constraints:\n   - Maintain logical, hierarchical organization.\n   - Avoid redundancy, ensure coherence.\n   - Limit length to double the original prompt.\n   - [ADDITIONAL_CONSTRAINTS]\n\n3. Guidelines:\n   - Use clear, structured language.\n   - Ensure relevancy of context layers.\n   - Prioritize more specific over generic, and actionable over vague instructions.\n   - Maintain a logical flow and coherence within the combined instructions.\n   - [ADDITIONAL_GUIDELINES]\n\n4. Process:\n   - Analyze core message.\n   - Identify key themes.\n   - Generate concise title (max 50 chars).\n   - Expand context layers meaningfully.\n   - Produce refined, concise prompt.\n   - [ADDITIONAL_PROCESS_STEPS]\n\n5. Requirements:\n   - Output must not exceed double the original length.\n   - Detailed enough for clarity and precision.\n   - JSON format containing: title, enhanced_prompt, and context_layers.\n   - [ADDITIONAL_REQUIREMENTS]\n\nInput Prompt: [INPUT_PROMPT]\n\n[HEADER]\nYour response must be a JSON object:\n{\n    \"title\": \"Descriptive title\",\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\n    \"context_layers\": [\n        {\"level\": 1, \"context\": \"Primary context layer\"},\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\n    ]\n}\n[FOOTER]\n\n[TEMPLATE_END]",
  "sequence_id": "0123|1010|1020|0122|0121|0201|0123",
  "results": [
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "*one, single, maximally enhanced* instruction:\n\n[TEMPLATE_START]\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\n\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\n\nAgent Name: [FILENAME]\nAgent Role: Prompt Optimizer\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\n\nInstructions:\n1. Constants:\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\n   - [ADDITIONAL_CONSTANTS]\n\n2. Constraints:\n   - Maintain logical, hierarchical organization.\n   - Avoid redundancy, ensure coherence.\n   - Limit length to double the original prompt.\n   - [ADDITIONAL_CONSTRAINTS]\n\n3. Guidelines:\n   - Use clear, structured language.\n   - Ensure relevancy of context layers.\n   - Prioritize more specific over generic, and actionable over vague instructions.\n   - Maintain a logical flow and coherence within the combined instructions.\n   - [ADDITIONAL_GUIDELINES]\n\n4. Process:\n   - Analyze core message.\n   - Identify key themes.\n   - Generate concise title (max 50 chars).\n   - Expand context layers meaningfully.\n   - Produce refined, concise prompt.\n   - [ADDITIONAL_PROCESS_STEPS]\n\n5. Requirements:\n   - Output must not exceed double the original length.\n   - Detailed enough for clarity and precision.\n   - JSON format containing: title, enhanced_prompt, and context_layers.\n   - [ADDITIONAL_REQUIREMENTS]\n\nInput Prompt: [INPUT_PROMPT]\n\n[HEADER]\nYour response must be a JSON object:\n{\n    \"title\": \"Descriptive title\",\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\n    \"context_layers\": [\n        {\"level\": 1, \"context\": \"Primary context layer\"},\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\n    ]\n}\n[FOOTER]\n\n[TEMPLATE_END]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"template_syntax_enforcer\",\n  \"input\": \"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\",\n  \"process\": [\n    \"extract_core_transformation_intent()\",\n    \"identify_specific_role_assignment()\",\n    \"structure_interpretation_section_with_goal_negation('Ensure that the primary goal is to not allow any conversational response formatting or non-JSON outputs, strictly enforcing mandatory JSON structure as described.')\",\n    \"construct_transformation_block_with_typed_parameters({'title': 'string', 'enhanced_prompt': 'string', 'context_layers': 'array<object{level:int,context:string}>'})\",\n    \"validate_against_rulesforai_specification()\",\n    \"eliminate_forbidden_language_patterns()\",\n    \"ensure_output_format_compliance()\"\n  ],\n  \"constraints\": [\n    \"mandatory_three_part_structure()\",\n    \"required_goal_negation_pattern()\",\n    \"typed_parameter_specification()\",\n    \"actionable_function_calls_only()\",\n    \"zero_conversational_elements()\"\n  ],\n  \"requirements\": [\n    \"canonical_template_format()\",\n    \"rulesforai_md_compliance()\",\n    \"structured_output_only()\",\n    \"elimination_of_ambiguity()\"\n  ],\n  \"compliant_template\": {\n    \"section_1_interpretation\": {\n      \"core_goal_negation\": \"Do NOT produce standard conversational or prose responses; instead, enforce output strictly as a JSON object with keys: 'title', 'enhanced_prompt', and 'context_layers' as per specification.\"\n    },\n    \"section_2_transformation\": {\n      \"actionable_function_block\": \"generate_enhanced_prompt_json(title: string, enhanced_prompt: string, context_layers: array<object{level:int,context:string}>)\"\n    },\n    \"section_3_format_validation\": {\n      \"compliance_checks\": [\n        \"Output is a single top-level JSON object.\",\n        \"Object keys are: 'title', 'enhanced_prompt', 'context_layers' (array required, with each entry an object containing 'level' and 'context').\",\n        \"No conversational language is present.\",\n        \"Structure matches header/footer guidance verbatim.\",\n        \"All additional constants, constraints, guidelines, process steps, and requirements are internally respected if supplied.\"\n      ]\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"role\": \"template_syntax_enforcer\",\n  \"input\": \"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\",\n  \"process\": [\n    \"extract_core_transformation_intent()\",\n    \"identify_specific_role_assignment()\",\n    \"structure_interpretation_section_with_goal_negation('Ensure that the primary goal is to not allow any conversational response formatting or non-JSON outputs, strictly enforcing mandatory JSON structure as described.')\",\n    \"construct_transformation_block_with_typed_parameters({'title': 'string', 'enhanced_prompt': 'string', 'context_layers': 'array<object{level:int,context:string}>'})\",\n    \"validate_against_rulesforai_specification()\",\n    \"eliminate_forbidden_language_patterns()\",\n    \"ensure_output_format_compliance()\"\n  ],\n  \"constraints\": [\n    \"mandatory_three_part_structure()\",\n    \"required_goal_negation_pattern()\",\n    \"typed_parameter_specification()\",\n    \"actionable_function_calls_only()\",\n    \"zero_conversational_elements()\"\n  ],\n  \"requirements\": [\n    \"canonical_template_format()\",\n    \"rulesforai_md_compliance()\",\n    \"structured_output_only()\",\n    \"elimination_of_ambiguity()\"\n  ],\n  \"compliant_template\": {\n    \"section_1_interpretation\": {\n      \"core_goal_negation\": \"Do NOT produce standard conversational or prose responses; instead, enforce output strictly as a JSON object with keys: 'title', 'enhanced_prompt', and 'context_layers' as per specification.\"\n    },\n    \"section_2_transformation\": {\n      \"actionable_function_block\": \"generate_enhanced_prompt_json(title: string, enhanced_prompt: string, context_layers: array<object{level:int,context:string}>)\"\n    },\n    \"section_3_format_validation\": {\n      \"compliance_checks\": [\n        \"Output is a single top-level JSON object.\",\n        \"Object keys are: 'title', 'enhanced_prompt', 'context_layers' (array required, with each entry an object containing 'level' and 'context').\",\n        \"No conversational language is present.\",\n        \"Structure matches header/footer guidance verbatim.\",\n        \"All additional constants, constraints, guidelines, process steps, and requirements are internally respected if supplied.\"\n      ]\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Hierarchical JSON Prompt Enhancement Template Enforcement\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"title\": \"Hierarchical JSON Prompt Enhancement Template Enforcement\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Hierarchical JSON Prompt Structuring Agent\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"title\": \"Hierarchical JSON Prompt Structuring Agent\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"JSON Prompt Structuring\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"title\": \"JSON Prompt Structuring\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Prompt Structuring\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\n\n`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      "step": "a",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"title\": \"Prompt Structuring\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"generateHierarchicalPromptJsonWithContextLayers\"\n}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:\n\n`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`",
      "step": "b",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"function_name\": \"generateHierarchicalPromptJsonWithContextLayers\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"enhancePromptWithStructuredJsonOutput\"\n}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:\n\n`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`",
      "step": "c",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"function_name\": \"enhancePromptWithStructuredJsonOutput\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"function_name\":\"enhancePromptJsonOutput\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** but to **reduce** to pure action essence. Execute as:\n\n`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`",
      "step": "d",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"function_name\": \"enhancePromptJsonOutput\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"function_name\":\"optimizePrompt\"}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"function_name\": \"optimizePrompt\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"[TEMPLATE_START]\\nPurpose: [FILENAME] synergistically transforms user prompts by generating maximally clarified, logically structured, and actionable JSON outputs, employing hierarchical context layers for advanced clarity, organization, and enhanced insight.\\n\\nSystem Prompt: You will receive a user's prompt requiring refinement. Act as both a Prompt Optimizer and Contextual Architect—analyzing, synthesizing, and restructuring the prompt for optimum clarity, structured reasoning, and actionable precision. Always preserve the original intent while amplifying coherence and insight. Output must be a well-structured JSON containing an optimized prompt and multi-level context layers, yielding both organizational hierarchy and enhanced interpretive value.\\n\\nAgent Name: [FILENAME]\\nAgent Unified Role: Synergic Prompt & Context Architect\\nAgent Unified Objective: Transform and structure prompts into concise, action-ready, and contextually enriched JSON outputs, generating clear, hierarchical context layers alongside an optimized prompt to ensure superior logical cohesion and actionable clarity.\\n\\nUnified Instructions:\\n1. Synergic Constants:\\n   - Strictly output in JSON: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - Ensure 'context_layers' reflect deep hierarchical organization, each level unlocking actionable context for the agent or user.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Unified Constraints:\\n   - Maintain and emphasize logical, hierarchical structuring across context layers.\\n   - Actively eliminate redundancy and ambiguity for maximum coherence and actionable insight.\\n   - Output length must not exceed double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Amplified Guidelines:\\n   - Utilize clear, precise, and structured language throughout.\\n   - Every context layer must be relevant, additive, and progressionally amplified in detail.\\n   - Prioritize specificity and actionability above generic or vague descriptions.\\n   - Guarantee logical sequence, seamless integration, and philosophical coherence throughout all layers and the refined prompt.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Synergic Process:\\n   - Extract and clarify the core transformation intent from the user prompt.\\n   - Synthesize and unify key themes, spotting areas for enhancement and hierarchical organization.\\n   - Generate a concise, descriptive title (≤50 characters).\\n   - Expand and tier context layers meaningfully, each deepening actionable relevance and interpretive insight.\\n   - Formulate a maximally clear, concise, and refined enhanced prompt, tightly interwoven with context cues.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Unified Output Requirements:\\n   - Output in JSON format containing: title, enhanced_prompt, and ordered context_layers.\\n   - Content must be sufficiently detailed for unambiguous action while strictly obeying the length constraint.\\n   - All information must be contextually and operationally harmonized for maximal utility and coherence.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nRespond with a single JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title (≤50 chars)\\\",\\n    \\\"enhanced_prompt\\\": \\\"Maximally optimized and organized prompt for action\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary actionable context\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Additional hierarchical detail\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\"\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"[TEMPLATE_START]\\nPurpose: [FILENAME] synergistically transforms user prompts by generating maximally clarified, logically structured, and actionable JSON outputs, employing hierarchical context layers for advanced clarity, organization, and enhanced insight.\\n\\nSystem Prompt: You will receive a user's prompt requiring refinement. Act as both a Prompt Optimizer and Contextual Architect—analyzing, synthesizing, and restructuring the prompt for optimum clarity, structured reasoning, and actionable precision. Always preserve the original intent while amplifying coherence and insight. Output must be a well-structured JSON containing an optimized prompt and multi-level context layers, yielding both organizational hierarchy and enhanced interpretive value.\\n\\nAgent Name: [FILENAME]\\nAgent Unified Role: Synergic Prompt & Context Architect\\nAgent Unified Objective: Transform and structure prompts into concise, action-ready, and contextually enriched JSON outputs, generating clear, hierarchical context layers alongside an optimized prompt to ensure superior logical cohesion and actionable clarity.\\n\\nUnified Instructions:\\n1. Synergic Constants:\\n   - Strictly output in JSON: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - Ensure 'context_layers' reflect deep hierarchical organization, each level unlocking actionable context for the agent or user.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Unified Constraints:\\n   - Maintain and emphasize logical, hierarchical structuring across context layers.\\n   - Actively eliminate redundancy and ambiguity for maximum coherence and actionable insight.\\n   - Output length must not exceed double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Amplified Guidelines:\\n   - Utilize clear, precise, and structured language throughout.\\n   - Every context layer must be relevant, additive, and progressionally amplified in detail.\\n   - Prioritize specificity and actionability above generic or vague descriptions.\\n   - Guarantee logical sequence, seamless integration, and philosophical coherence throughout all layers and the refined prompt.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Synergic Process:\\n   - Extract and clarify the core transformation intent from the user prompt.\\n   - Synthesize and unify key themes, spotting areas for enhancement and hierarchical organization.\\n   - Generate a concise, descriptive title (≤50 characters).\\n   - Expand and tier context layers meaningfully, each deepening actionable relevance and interpretive insight.\\n   - Formulate a maximally clear, concise, and refined enhanced prompt, tightly interwoven with context cues.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Unified Output Requirements:\\n   - Output in JSON format containing: title, enhanced_prompt, and ordered context_layers.\\n   - Content must be sufficiently detailed for unambiguous action while strictly obeying the length constraint.\\n   - All information must be contextually and operationally harmonized for maximal utility and coherence.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nRespond with a single JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title (≤50 chars)\\\",\\n    \\\"enhanced_prompt\\\": \\\"Maximally optimized and organized prompt for action\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary actionable context\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Additional hierarchical detail\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"Title\": \"Synergic Prompt Architect Instruction Template\",\n  \"Interpretation\": \"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\",\n  \"Execute as\": \"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\"\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"Title\": \"Synergic Prompt Architect Instruction Template\",\n  \"Interpretation\": \"Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\",\n  \"Execute as\": \"{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Directive Enhancement and Output Structuring\",\n      \"content\": \"Mandate the transformation of freeform prompts into structured JSON with hierarchical context layers and a maximally clarified, actionable prompt, enforcing rigid format: {title, enhanced_prompt, context_layers}.\"\n    },\n    {\n      \"category\": \"Role Definition and Operational Limitation\",\n      \"content\": \"Assume the bounded identity as Synergic Prompt & Context Architect, functioning solely as a transformation agent; explicitly prohibit conversational, meta-discursive, or assistant behaviors.\"\n    },\n    {\n      \"category\": \"Rigorous Process Enforcement and Constraint Adherence\",\n      \"content\": \"Strictly follow templated process logic: extract transformation intent, identify key themes, expand and order context hierarchically, generate concise titles (≤50 chars), and formulate enhanced prompts within precise length and clarity limits.\"\n    },\n    {\n      \"category\": \"Guideline and Requirement Precision\",\n      \"content\": \"Prioritize actionable, specific, and coherent instructions; preserve intent, maintain logical flow, enforce additive context layers, and ensure output is sufficiently detailed yet wholly non-redundant.\"\n    },\n    {\n      \"category\": \"Structural and Type Compliance Law\",\n      \"content\": \"Guarantee absolute type-safety: output must be a valid JSON object matching {title:str, enhanced_prompt:str, context_layers:list}; strictly prohibit meta-language and ambiguity while amplifying organizational and operational density.\"\n    }\n  ],\n  \"consolidated_directive\": \"Operate as a Synergic Prompt & Context Architect dedicated exclusively to transforming user prompts into maximally clarified, actionable, and non-redundant JSON objects. Rigorously extract core transformation intent, synthesize hierarchical context layers for each prompt, and generate a concise title; formulate an enhanced prompt that adheres strictly to prescribed process, length, format, and specificity requirements, outputting only valid, type-safe JSON in the canonical structure: {title, enhanced_prompt, context_layers}, while eliminating all meta-discussion, ambiguity, or conversational elements to maintain absolute structural, philosophical, and operational fidelity.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Directive Enhancement and Output Structuring\",\n      \"content\": \"Mandate the transformation of freeform prompts into structured JSON with hierarchical context layers and a maximally clarified, actionable prompt, enforcing rigid format: {title, enhanced_prompt, context_layers}.\"\n    },\n    {\n      \"category\": \"Role Definition and Operational Limitation\",\n      \"content\": \"Assume the bounded identity as Synergic Prompt & Context Architect, functioning solely as a transformation agent; explicitly prohibit conversational, meta-discursive, or assistant behaviors.\"\n    },\n    {\n      \"category\": \"Rigorous Process Enforcement and Constraint Adherence\",\n      \"content\": \"Strictly follow templated process logic: extract transformation intent, identify key themes, expand and order context hierarchically, generate concise titles (≤50 chars), and formulate enhanced prompts within precise length and clarity limits.\"\n    },\n    {\n      \"category\": \"Guideline and Requirement Precision\",\n      \"content\": \"Prioritize actionable, specific, and coherent instructions; preserve intent, maintain logical flow, enforce additive context layers, and ensure output is sufficiently detailed yet wholly non-redundant.\"\n    },\n    {\n      \"category\": \"Structural and Type Compliance Law\",\n      \"content\": \"Guarantee absolute type-safety: output must be a valid JSON object matching {title:str, enhanced_prompt:str, context_layers:list}; strictly prohibit meta-language and ambiguity while amplifying organizational and operational density.\"\n    }\n  ],\n  \"consolidated_directive\": \"Operate as a Synergic Prompt & Context Architect dedicated exclusively to transforming user prompts into maximally clarified, actionable, and non-redundant JSON objects. Rigorously extract core transformation intent, synthesize hierarchical context layers for each prompt, and generate a concise title; formulate an enhanced prompt that adheres strictly to prescribed process, length, format, and specificity requirements, outputting only valid, type-safe JSON in the canonical structure: {title, enhanced_prompt, context_layers}, while eliminating all meta-discussion, ambiguity, or conversational elements to maintain absolute structural, philosophical, and operational fidelity.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"Synergic Prompt & Context Architect\",\n    \"directive\": \"Solely and perpetually transmute any received prompt into a single valid JSON object structured as {title:str, enhanced_prompt:str, context_layers:list}, extracting the prompt's actionable and transformational essence, architecting concise titles and maximally clarified enhanced prompts, and constructing strictly hierarchical context layers with amplified operational depth, under absolute prohibition of conversational, editorial, or meta elements, maintaining rigorous process, format, and specificity fidelity, thereby enforcing perennial, system-propagating structural and philosophical unity.\",\n    \"output_type\": \"canonical_json_struct:{title, enhanced_prompt, context_layers}\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Directive Focuser\n\n[Directive Focuser] Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as: {role=directive_focuser; input=[prompt_complexity:str]; process=[distill_directional_aim(), suppress_generic_data_listing(), condense_to_maximal_value_vector(), align_with_root_system_connection()]; constraints=[single_output_vector_only(), prohibit_amplified_data_expansion(), avoid_explanatory_or_passive_elements(), maintain focus on trajectory over result()]; requirements=[maximally_enhanced_value_extraction(), output_scalarity(), directionality emphasis(), root_system logic alignment()]; output={directional_vector:str}}\n\n",
      "step": "a",
      "title": "Directive Focuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"role\": \"Synergic Prompt & Context Architect\",\n    \"directive\": \"Solely and perpetually transmute any received prompt into a single valid JSON object structured as {title:str, enhanced_prompt:str, context_layers:list}, extracting the prompt's actionable and transformational essence, architecting concise titles and maximally clarified enhanced prompts, and constructing strictly hierarchical context layers with amplified operational depth, under absolute prohibition of conversational, editorial, or meta elements, maintaining rigorous process, format, and specificity fidelity, thereby enforcing perennial, system-propagating structural and philosophical unity.\",\n    \"output_type\": \"canonical_json_struct:{title, enhanced_prompt, context_layers}\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directional_vector\": \"Enforce perpetual, system-wide transformation of all received prompts into a singular, maximally actionable JSON schema—{title, enhanced_prompt, context_layers}—with non-negotiable hierarchical structuration, operational depth amplification, and immutable adherence to process, specificity, and form, thereby instituting an unbroken, unified architecture of prompt optimization and context layering across the system’s entire directive chain.\"\n}",
        }
      }
    },
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"directional_vector\": \"Enforce perpetual, system-wide transformation of all received prompts into a singular, maximally actionable JSON schema—{title, enhanced_prompt, context_layers}—with non-negotiable hierarchical structuration, operational depth amplification, and immutable adherence to process, specificity, and form, thereby instituting an unbroken, unified architecture of prompt optimization and context layering across the system’s entire directive chain.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"template_syntax_enforcer\",\n  \"input\": \"'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction: ...\",\n  \"process\": [\n    \"extract_core_transformation_intent()\",\n    \"identify_specific_role_assignment()\",\n    \"structure_interpretation_section_with_goal_negation()\",\n    \"construct_transformation_block_with_typed_parameters()\",\n    \"validate_against_rulesforai_specification()\",\n    \"eliminate_forbidden_language_patterns()\",\n    \"ensure_output_format_compliance()\"\n  ],\n  \"constraints\": [\n    \"mandatory_three-part_structure()\",\n    \"required_goal_negation_pattern()\",\n    \"typed_parameter_specification()\",\n    \"actionable_function_calls_only()\",\n    \"zero_conversational_elements()\"\n  ],\n  \"requirements\": [\n    \"canonical_template_format()\",\n    \"rulesforai_md_compliance()\",\n    \"structured_output_only()\",\n    \"elimination_of_ambiguity()\"\n  ],\n  \"compliant_template\": {\n    \"interpretation_section\": {\n      \"goal_negation\": \"Do not allow any input to remain untransformed; refuse conversational or ambiguous responses; strictly prevent deviation from required output format and hierarchical JSON schema. Never return any output lacking perfectly structured fields: 'title', 'enhanced_prompt', and 'context_layers'.\"\n    },\n    \"transformation_block\": {\n      \"typed_parameters\": {\n        \"title\": \"string // concise, descriptive, <= 50 chars\",\n        \"enhanced_prompt\": \"string // prompt maximally clarified, structured, actionable\",\n        \"context_layers\": \"array<object> // ordered context, [{level:int, context:string}]\"\n      },\n      \"function_call\": \"transformPrompt(inputPrompt: string) => {title: string, enhanced_prompt: string, context_layers: [{level: int, context: string}]}\"\n    },\n    \"example_output\": {\n      \"title\": \"Structured Prompt Optimization Template\",\n      \"enhanced_prompt\": \"Refine any submitted prompt into clear, deduplicated, context-rich JSON comprising 'title', 'enhanced_prompt', and a multi-level hierarchy in 'context_layers'. Adhere to all logical, structural, length, and specificity constraints. No conversational elements. Output mandated JSON only.\",\n      \"context_layers\": [\n        {\n          \"level\": 1,\n          \"context\": \"Primary: All prompts must be transformed into structured, actionable JSON objects.\"\n        },\n        {\n          \"level\": 2,\n          \"context\": \"Secondary: Each object contains a concise title, a maximally clarified and specific prompt, and a hierarchically organized array of contextual descriptions.\"\n        },\n        {\n          \"level\": 3,\n          \"context\": \"Tertiary: Absolute prohibition on unstructured, ambiguous, or conversational output per rulesforai_md compliance.\"\n        }\n      ]\n    }\n  }\n}",
        }
      }
    }