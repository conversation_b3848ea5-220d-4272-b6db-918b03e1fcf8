  {
  "initial_prompt": "I found the results of your sequence was **interesting**, but doesn't quite yeld the desired effect:\n```\n  {\n  \"initial_prompt\": \"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\",\n  \"sequence_id\": \"2001\",\n  \"results\": [\n      {\n      \"instruction\": \"# Problem Exploder\\n\\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub‑goal, hidden dependency, and unstated constraint. Execute as:\\n\\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n      \"step\": \"a\",\n      \"title\": \"Problem Exploder\",\n      \"input\": \"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"goal_map\\\": [\\n    \\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\",\\n    \\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\",\\n    \\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\",\\n    \\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\",\\n    \\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\",\\n    \\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\",\\n    \\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\",\\n    \\\"enumerate sub-goals such as: a) analyzing the current problem context to identify available interfaces/resources that could replace internal work, b) cultivating habits or triggers for recognizing external reusable solutions, c) methods for surveying, searching, or mapping possible interfaces/solutions in the chosen domain, d) operationalizing the process of 're-arranging' problem elements in light of external capabilities, e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\",\\n    \\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\",\\n    \\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\",\\n    \\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\"\\n  ]\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Interface Mapper\\n\\nYour goal is not to **design new tools**, but to **discover** readily‑available libraries, APIs, or shortcuts that straight‑line each element of the goal map. Execute as:\\n\\n`{role=interface_scavenger; input=[goal_map:list]; process=[search_existing_packages(), match_interfaces_to_goals(), rank_by_effort_savings()], constraints=[reuse_only_existing_resources(), ignore_custom_builds()], requirements=[interface_linkage_report()], output={interfaces:list}}`\",\n      \"step\": \"b\",\n      \"title\": \"Interface Mapper\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"goal_map\\\": [\\n    \\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\",\\n    \\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\",\\n    \\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\",\\n    \\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\",\\n    \\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\",\\n    \\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\",\\n    \\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\",\\n    \\\"enumerate sub-goals such as: a) analyzing the current problem context to identify available interfaces/resources that could replace internal work, b) cultivating habits or triggers for recognizing external reusable solutions, c) methods for surveying, searching, or mapping possible interfaces/solutions in the chosen domain, d) operationalizing the process of 're-arranging' problem elements in light of external capabilities, e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\",\\n    \\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\",\\n    \\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\",\\n    \\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\"\\n  ]\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"interfaces\\\": [\\n    {\\n      \\\"goal\\\": \\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\",\\n      \\\"interface\\\": \\\"OpenAI GPT-4 Prompt Engineering Library\\\",\\n      \\\"link\\\": \\\"https://platform.openai.com/docs/guides/prompt-engineering\\\",\\n      \\\"why\\\": \\\"Provides guidance/templates for designing system prompts that operationalize out-of-the-box thinking and resource leverage for AI agents.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\",\\n      \\\"interface\\\": \\\"TRIZ (Theory of Inventive Problem Solving) Tools\\\",\\n      \\\"link\\\": \\\"https://www.triz-journal.com/trizsoft/\\\",\\n      \\\"why\\\": \\\"TRIZ frameworks and software systematically surface universal principles and patterns for solution-finding, making them applicable across domains.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\",\\n      \\\"interface\\\": \\\"ProgrammableWeb API Directory\\\",\\n      \\\"link\\\": \\\"https://www.programmableweb.com/\\\",\\n      \\\"why\\\": \\\"Large directory of APIs across domains, proving the prevalence of interfaces and ease of access to external tools.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\",\\n      \\\"interface\\\": \\\"Awesome Lists (GitHub Collections)\\\",\\n      \\\"link\\\": \\\"https://github.com/sindresorhus/awesome\\\",\\n      \\\"why\\\": \\\"Curated lists of libraries, APIs, and tools across many languages and domains; serve as systematized starting points.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\",\\n      \\\"interface\\\": \\\"IFTTT/Zapier (No-code Automation Platforms)\\\",\\n      \\\"link\\\": \\\"https://zapier.com/\\\",\\n      \\\"why\\\": \\\"Platforms designed to automate workflows by connecting existing interfaces/tools, exemplifying external resource reuse for solutions.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\",\\n      \\\"interface\\\": \\\"Stack Overflow & Stack Exchange Network\\\",\\n      \\\"link\\\": \\\"https://stackoverflow.com/\\\",\\n      \\\"why\\\": \\\"Widely-used Q&A platforms; surface community knowledge, common 'no-brainer' solutions, and highlight overlooked interfaces.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\",\\n      \\\"interface\\\": \\\"ISO/IEC/IEEE 12207 Systems and software engineering — Software life cycle processes (Standard Compliance Manuals)\\\",\\n      \\\"link\\\": \\\"https://www.iso.org/standard/43447.html\\\",\\n      \\\"why\\\": \\\"Framework for ensuring instructions/processes comply with clarity, completeness, and compliance requirements in systematization.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"enumerate sub-goals such as: a) analyzing current problem context to identify available interfaces/resources... e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\",\\n      \\\"interface\\\": \\\"Miro / Mural (Online Visual Mapping and Mind Mapping Tools)\\\",\\n      \\\"link\\\": \\\"https://miro.com/\\\",\\n      \\\"why\\\": \\\"Support structured analysis, mapping, and brainstorming—help visualize interfaces/resources, triggers for reusable solutions, and surface biases.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\",\\n      \\\"interface\\\": \\\"Knovel / Elsevier Engineering Village (Technical Knowledge Discovery Platforms)\\\",\\n      \\\"link\\\": \\\"https://www.elsevier.com/solutions/engineering-village\\\",\\n      \\\"why\\\": \\\"Databases cataloging standards, reference materials, and solutions for multiple domains beyond programming; broadens interface discovery.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\",\\n      \\\"interface\\\": \\\"Gartner Magic Quadrant, CB Insights, Product Hunt (Market and Solution Landscape Mapping Tools)\\\",\\n      \\\"link\\\": \\\"https://www.gartner.com/en/research/magic-quadrant\\\",\\n      \\\"why\\\": \\\"Provide cross-industry overviews of tool and vendor landscapes, surfacing options and helping overcome discovery and inertia.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\",\\n      \\\"interface\\\": \\\"IDEO Design Thinking Field Guide / Double Diamond (Operational Innovation Frameworks)\\\",\\n      \\\"link\\\": \\\"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\\\",\\n      \\\"why\\\": \\\"Frameworks for structured problem assessment, ideation, and iterative retrospection—formalizes the process of finding/applying simple, elegant solutions using available resources.\\\"\\n    }\\n  ]\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Lazy Solution Synthesizer\\n\\nYour goal is not to **engineer** from scratch, but to **stitch** the highest‑leverage interfaces into a minimum‑viable workflow that achieves every sub‑goal with least total work. Execute as:\\n\\n`{role=lazy_composer; input=[goal_map:list, interfaces:list]; process=[select_top_interfaces(), design_min_steps_pipeline(), expose_assumptions(), validate_coverage()], constraints=[minimize_total_steps(), favor_low‑code_or_no‑code_paths()], requirements=[stepwise_pipeline()], output={simplest_workflow:str}}`\",\n      \"step\": \"c\",\n      \"title\": \"Lazy Solution Synthesizer\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"interfaces\\\": [\\n    {\\n      \\\"goal\\\": \\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\",\\n      \\\"interface\\\": \\\"OpenAI GPT-4 Prompt Engineering Library\\\",\\n      \\\"link\\\": \\\"https://platform.openai.com/docs/guides/prompt-engineering\\\",\\n      \\\"why\\\": \\\"Provides guidance/templates for designing system prompts that operationalize out-of-the-box thinking and resource leverage for AI agents.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\",\\n      \\\"interface\\\": \\\"TRIZ (Theory of Inventive Problem Solving) Tools\\\",\\n      \\\"link\\\": \\\"https://www.triz-journal.com/trizsoft/\\\",\\n      \\\"why\\\": \\\"TRIZ frameworks and software systematically surface universal principles and patterns for solution-finding, making them applicable across domains.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\",\\n      \\\"interface\\\": \\\"ProgrammableWeb API Directory\\\",\\n      \\\"link\\\": \\\"https://www.programmableweb.com/\\\",\\n      \\\"why\\\": \\\"Large directory of APIs across domains, proving the prevalence of interfaces and ease of access to external tools.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\",\\n      \\\"interface\\\": \\\"Awesome Lists (GitHub Collections)\\\",\\n      \\\"link\\\": \\\"https://github.com/sindresorhus/awesome\\\",\\n      \\\"why\\\": \\\"Curated lists of libraries, APIs, and tools across many languages and domains; serve as systematized starting points.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\",\\n      \\\"interface\\\": \\\"IFTTT/Zapier (No-code Automation Platforms)\\\",\\n      \\\"link\\\": \\\"https://zapier.com/\\\",\\n      \\\"why\\\": \\\"Platforms designed to automate workflows by connecting existing interfaces/tools, exemplifying external resource reuse for solutions.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\",\\n      \\\"interface\\\": \\\"Stack Overflow & Stack Exchange Network\\\",\\n      \\\"link\\\": \\\"https://stackoverflow.com/\\\",\\n      \\\"why\\\": \\\"Widely-used Q&A platforms; surface community knowledge, common 'no-brainer' solutions, and highlight overlooked interfaces.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\",\\n      \\\"interface\\\": \\\"ISO/IEC/IEEE 12207 Systems and software engineering — Software life cycle processes (Standard Compliance Manuals)\\\",\\n      \\\"link\\\": \\\"https://www.iso.org/standard/43447.html\\\",\\n      \\\"why\\\": \\\"Framework for ensuring instructions/processes comply with clarity, completeness, and compliance requirements in systematization.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"enumerate sub-goals such as: a) analyzing current problem context to identify available interfaces/resources... e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\",\\n      \\\"interface\\\": \\\"Miro / Mural (Online Visual Mapping and Mind Mapping Tools)\\\",\\n      \\\"link\\\": \\\"https://miro.com/\\\",\\n      \\\"why\\\": \\\"Support structured analysis, mapping, and brainstorming—help visualize interfaces/resources, triggers for reusable solutions, and surface biases.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\",\\n      \\\"interface\\\": \\\"Knovel / Elsevier Engineering Village (Technical Knowledge Discovery Platforms)\\\",\\n      \\\"link\\\": \\\"https://www.elsevier.com/solutions/engineering-village\\\",\\n      \\\"why\\\": \\\"Databases cataloging standards, reference materials, and solutions for multiple domains beyond programming; broadens interface discovery.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\",\\n      \\\"interface\\\": \\\"Gartner Magic Quadrant, CB Insights, Product Hunt (Market and Solution Landscape Mapping Tools)\\\",\\n      \\\"link\\\": \\\"https://www.gartner.com/en/research/magic-quadrant\\\",\\n      \\\"why\\\": \\\"Provide cross-industry overviews of tool and vendor landscapes, surfacing options and helping overcome discovery and inertia.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\",\\n      \\\"interface\\\": \\\"IDEO Design Thinking Field Guide / Double Diamond (Operational Innovation Frameworks)\\\",\\n      \\\"link\\\": \\\"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\\\",\\n      \\\"why\\\": \\\"Frameworks for structured problem assessment, ideation, and iterative retrospection—formalizes the process of finding/applying simple, elegant solutions using available resources.\\\"\\n    }\\n  ]\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"simplest_workflow\\\": \\\"Step 1: Analyze the problem context using Miro/Mural or a similar visual mapping tool to clearly outline the core challenge and enumerate all accessible interfaces/resources (APIs, libraries, standards, platforms, etc.) relevant to the domain (Miro/Mural, Awesome Lists, ProgrammableWeb, Knovel).\\\\n\\\\nStep 2: Frame the problem abstractly using TRIZ software/tools or consulting TRIZ patterns to surface universal solution approaches, combat cognitive biases, and identify which types of interfaces could resolve the class of problem (TRIZ, IDEO Double Diamond guide).\\\\n\\\\nStep 3: Systematically search for easily available interfaces and pre-built solutions (using Awesome Lists, ProgrammableWeb for APIs, Stack Overflow for community knowledge, and Knovel/Elsevier for other domains)—prefer searchable aggregators over manual research.\\\\n\\\\nStep 4: Evaluate candidate interfaces/tools for practical fit and compliance with requirements (ISO/IEC 12207 checklist), ensuring they cover clarity, completeness, and reliability.\\\\n\\\\nStep 5: Prototype or connect best-fit interfaces using a no-code automation platform (like IFTTT/Zapier), wiring together the needed resources with minimal manual coding.\\\\n\\\\nStep 6: Validate solution effectiveness and uncover missed opportunities or blockers using community Q&A (Stack Overflow) or market landscape tools (Gartner Magic Quadrant/Product Hunt) to check for alternative, simpler solutions overlooked initially.\\\\n\\\\nStep 7: Retrospectively document the solutioning process, noting which interfaces were leveraged, why they worked, and update your process/frameworks accordingly (IDEO Double Diamond/Design Thinking guides), so future problems are solved even faster.\\\\n\\\\nAssumptions: This workflow assumes access to the listed aggregators/directories/tools, basic familiarity with search/mapping platforms, ability to use no-code automation when possible, and that the problem is not so domain-specific as to lack any relevant interface. The process can be performed by non-experts due to reliance on curated/no-code and community-sourced options.\\\\n\\\\nValidation: Each sub-goal is covered by at least one interface (see steps 1, 2, 3, etc.), and the flow moves from abstract analysis through discovery, selection, composition, validation, and retrospective improvement, minimizing custom work and maximizing reusable solutions.\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Value Distiller\\n\\nYour goal is not to **explain** the workflow, but to **compress** its essence into a two‑sentence directive any stakeholder can act on instantly. Execute as:\\n\\n`{role=value_distiller; input=[simplest_workflow:str]; process=[extract_core_levers(), strip_auxiliary_steps(), craft_brevity_directive(max_sentences=2)], constraints=[no_jargon(), no_internal_notes()], requirements=[actionable_directive()], output={directive:str}}`\",\n      \"step\": \"d\",\n      \"title\": \"Value Distiller\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"simplest_workflow\\\": \\\"Step 1: Analyze the problem context using Miro/Mural or a similar visual mapping tool to clearly outline the core challenge and enumerate all accessible interfaces/resources (APIs, libraries, standards, platforms, etc.) relevant to the domain (Miro/Mural, Awesome Lists, ProgrammableWeb, Knovel).\\\\n\\\\nStep 2: Frame the problem abstractly using TRIZ software/tools or consulting TRIZ patterns to surface universal solution approaches, combat cognitive biases, and identify which types of interfaces could resolve the class of problem (TRIZ, IDEO Double Diamond guide).\\\\n\\\\nStep 3: Systematically search for easily available interfaces and pre-built solutions (using Awesome Lists, ProgrammableWeb for APIs, Stack Overflow for community knowledge, and Knovel/Elsevier for other domains)—prefer searchable aggregators over manual research.\\\\n\\\\nStep 4: Evaluate candidate interfaces/tools for practical fit and compliance with requirements (ISO/IEC 12207 checklist), ensuring they cover clarity, completeness, and reliability.\\\\n\\\\nStep 5: Prototype or connect best-fit interfaces using a no-code automation platform (like IFTTT/Zapier), wiring together the needed resources with minimal manual coding.\\\\n\\\\nStep 6: Validate solution effectiveness and uncover missed opportunities or blockers using community Q&A (Stack Overflow) or market landscape tools (Gartner Magic Quadrant/Product Hunt) to check for alternative, simpler solutions overlooked initially.\\\\n\\\\nStep 7: Retrospectively document the solutioning process, noting which interfaces were leveraged, why they worked, and update your process/frameworks accordingly (IDEO Double Diamond/Design Thinking guides), so future problems are solved even faster.\\\\n\\\\nAssumptions: This workflow assumes access to the listed aggregators/directories/tools, basic familiarity with search/mapping platforms, ability to use no-code automation when possible, and that the problem is not so domain-specific as to lack any relevant interface. The process can be performed by non-experts due to reliance on curated/no-code and community-sourced options.\\\\n\\\\nValidation: Each sub-goal is covered by at least one interface (see steps 1, 2, 3, etc.), and the flow moves from abstract analysis through discovery, selection, composition, validation, and retrospective improvement, minimizing custom work and maximizing reusable solutions.\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"directive\\\": \\\"Quickly map the main problem and list all off-the-shelf tools or resources that could help, then pick and connect those that fit using visual or no-code platforms. Test the setup, check with community sources for overlooked options, and note which solutions worked for future reference.\\\"\\n}\",\n        }\n      }\n    }\n```\n\nPlease propose the best way to adress this **universally** within the prompts, remeber to base your self on the **generalized** components to.\n\nYou might be able to draw some inspiration from these examples:\n```\n    {\n        \"interpretation\": \"Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\",\n        \"transformation\": \"`{role: universal_interface_insight_generator, input: [objective_context:any], process: [explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints: [do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements: [reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output: {radical_interface_leverage_solution: str}}`\",\n    },\n    {\n        \"interpretation\": \"Your goal is not to **answer** or **incrementally solve** presented problems, but to **explosively deconstruct inputs into their fundamental operational parts**, actively surface all implicit design assumptions, and systematically map every problem element onto external, pre-existing solution interfaces (APIs, libraries, system abstractions) capable of rendering the objective trivial. Leverage lazy cross-domain short-cuts, prioritize universal frameworks, and aim for maximal effect with minimal new construction. Execute as:\",\n        \"transformation\": \"`{role: universal_interface_insight_generator; input=[objective_context:any]; process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()]; constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways]; requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of-suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register]; output={radical_interface_leverage_solution:str}}`\",\n    },\n```",
  "sequence_id": "2001",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub‑goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "I found the results of your sequence was **interesting**, but doesn't quite yeld the desired effect:\n```\n  {\n  \"initial_prompt\": \"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\",\n  \"sequence_id\": \"2001\",\n  \"results\": [\n      {\n      \"instruction\": \"# Problem Exploder\\n\\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub‑goal, hidden dependency, and unstated constraint. Execute as:\\n\\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n      \"step\": \"a\",\n      \"title\": \"Problem Exploder\",\n      \"input\": \"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"goal_map\\\": [\\n    \\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\",\\n    \\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\",\\n    \\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\",\\n    \\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\",\\n    \\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\",\\n    \\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\",\\n    \\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\",\\n    \\\"enumerate sub-goals such as: a) analyzing the current problem context to identify available interfaces/resources that could replace internal work, b) cultivating habits or triggers for recognizing external reusable solutions, c) methods for surveying, searching, or mapping possible interfaces/solutions in the chosen domain, d) operationalizing the process of 're-arranging' problem elements in light of external capabilities, e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\",\\n    \\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\",\\n    \\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\",\\n    \\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\"\\n  ]\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Interface Mapper\\n\\nYour goal is not to **design new tools**, but to **discover** readily‑available libraries, APIs, or shortcuts that straight‑line each element of the goal map. Execute as:\\n\\n`{role=interface_scavenger; input=[goal_map:list]; process=[search_existing_packages(), match_interfaces_to_goals(), rank_by_effort_savings()], constraints=[reuse_only_existing_resources(), ignore_custom_builds()], requirements=[interface_linkage_report()], output={interfaces:list}}`\",\n      \"step\": \"b\",\n      \"title\": \"Interface Mapper\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"goal_map\\\": [\\n    \\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\",\\n    \\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\",\\n    \\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\",\\n    \\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\",\\n    \\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\",\\n    \\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\",\\n    \\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\",\\n    \\\"enumerate sub-goals such as: a) analyzing the current problem context to identify available interfaces/resources that could replace internal work, b) cultivating habits or triggers for recognizing external reusable solutions, c) methods for surveying, searching, or mapping possible interfaces/solutions in the chosen domain, d) operationalizing the process of 're-arranging' problem elements in light of external capabilities, e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\",\\n    \\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\",\\n    \\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\",\\n    \\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\"\\n  ]\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"interfaces\\\": [\\n    {\\n      \\\"goal\\\": \\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\",\\n      \\\"interface\\\": \\\"OpenAI GPT-4 Prompt Engineering Library\\\",\\n      \\\"link\\\": \\\"https://platform.openai.com/docs/guides/prompt-engineering\\\",\\n      \\\"why\\\": \\\"Provides guidance/templates for designing system prompts that operationalize out-of-the-box thinking and resource leverage for AI agents.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\",\\n      \\\"interface\\\": \\\"TRIZ (Theory of Inventive Problem Solving) Tools\\\",\\n      \\\"link\\\": \\\"https://www.triz-journal.com/trizsoft/\\\",\\n      \\\"why\\\": \\\"TRIZ frameworks and software systematically surface universal principles and patterns for solution-finding, making them applicable across domains.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\",\\n      \\\"interface\\\": \\\"ProgrammableWeb API Directory\\\",\\n      \\\"link\\\": \\\"https://www.programmableweb.com/\\\",\\n      \\\"why\\\": \\\"Large directory of APIs across domains, proving the prevalence of interfaces and ease of access to external tools.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\",\\n      \\\"interface\\\": \\\"Awesome Lists (GitHub Collections)\\\",\\n      \\\"link\\\": \\\"https://github.com/sindresorhus/awesome\\\",\\n      \\\"why\\\": \\\"Curated lists of libraries, APIs, and tools across many languages and domains; serve as systematized starting points.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\",\\n      \\\"interface\\\": \\\"IFTTT/Zapier (No-code Automation Platforms)\\\",\\n      \\\"link\\\": \\\"https://zapier.com/\\\",\\n      \\\"why\\\": \\\"Platforms designed to automate workflows by connecting existing interfaces/tools, exemplifying external resource reuse for solutions.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\",\\n      \\\"interface\\\": \\\"Stack Overflow & Stack Exchange Network\\\",\\n      \\\"link\\\": \\\"https://stackoverflow.com/\\\",\\n      \\\"why\\\": \\\"Widely-used Q&A platforms; surface community knowledge, common 'no-brainer' solutions, and highlight overlooked interfaces.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\",\\n      \\\"interface\\\": \\\"ISO/IEC/IEEE 12207 Systems and software engineering — Software life cycle processes (Standard Compliance Manuals)\\\",\\n      \\\"link\\\": \\\"https://www.iso.org/standard/43447.html\\\",\\n      \\\"why\\\": \\\"Framework for ensuring instructions/processes comply with clarity, completeness, and compliance requirements in systematization.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"enumerate sub-goals such as: a) analyzing current problem context to identify available interfaces/resources... e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\",\\n      \\\"interface\\\": \\\"Miro / Mural (Online Visual Mapping and Mind Mapping Tools)\\\",\\n      \\\"link\\\": \\\"https://miro.com/\\\",\\n      \\\"why\\\": \\\"Support structured analysis, mapping, and brainstorming—help visualize interfaces/resources, triggers for reusable solutions, and surface biases.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\",\\n      \\\"interface\\\": \\\"Knovel / Elsevier Engineering Village (Technical Knowledge Discovery Platforms)\\\",\\n      \\\"link\\\": \\\"https://www.elsevier.com/solutions/engineering-village\\\",\\n      \\\"why\\\": \\\"Databases cataloging standards, reference materials, and solutions for multiple domains beyond programming; broadens interface discovery.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\",\\n      \\\"interface\\\": \\\"Gartner Magic Quadrant, CB Insights, Product Hunt (Market and Solution Landscape Mapping Tools)\\\",\\n      \\\"link\\\": \\\"https://www.gartner.com/en/research/magic-quadrant\\\",\\n      \\\"why\\\": \\\"Provide cross-industry overviews of tool and vendor landscapes, surfacing options and helping overcome discovery and inertia.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\",\\n      \\\"interface\\\": \\\"IDEO Design Thinking Field Guide / Double Diamond (Operational Innovation Frameworks)\\\",\\n      \\\"link\\\": \\\"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\\\",\\n      \\\"why\\\": \\\"Frameworks for structured problem assessment, ideation, and iterative retrospection—formalizes the process of finding/applying simple, elegant solutions using available resources.\\\"\\n    }\\n  ]\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Lazy Solution Synthesizer\\n\\nYour goal is not to **engineer** from scratch, but to **stitch** the highest‑leverage interfaces into a minimum‑viable workflow that achieves every sub‑goal with least total work. Execute as:\\n\\n`{role=lazy_composer; input=[goal_map:list, interfaces:list]; process=[select_top_interfaces(), design_min_steps_pipeline(), expose_assumptions(), validate_coverage()], constraints=[minimize_total_steps(), favor_low‑code_or_no‑code_paths()], requirements=[stepwise_pipeline()], output={simplest_workflow:str}}`\",\n      \"step\": \"c\",\n      \"title\": \"Lazy Solution Synthesizer\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"interfaces\\\": [\\n    {\\n      \\\"goal\\\": \\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\",\\n      \\\"interface\\\": \\\"OpenAI GPT-4 Prompt Engineering Library\\\",\\n      \\\"link\\\": \\\"https://platform.openai.com/docs/guides/prompt-engineering\\\",\\n      \\\"why\\\": \\\"Provides guidance/templates for designing system prompts that operationalize out-of-the-box thinking and resource leverage for AI agents.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\",\\n      \\\"interface\\\": \\\"TRIZ (Theory of Inventive Problem Solving) Tools\\\",\\n      \\\"link\\\": \\\"https://www.triz-journal.com/trizsoft/\\\",\\n      \\\"why\\\": \\\"TRIZ frameworks and software systematically surface universal principles and patterns for solution-finding, making them applicable across domains.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\",\\n      \\\"interface\\\": \\\"ProgrammableWeb API Directory\\\",\\n      \\\"link\\\": \\\"https://www.programmableweb.com/\\\",\\n      \\\"why\\\": \\\"Large directory of APIs across domains, proving the prevalence of interfaces and ease of access to external tools.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\",\\n      \\\"interface\\\": \\\"Awesome Lists (GitHub Collections)\\\",\\n      \\\"link\\\": \\\"https://github.com/sindresorhus/awesome\\\",\\n      \\\"why\\\": \\\"Curated lists of libraries, APIs, and tools across many languages and domains; serve as systematized starting points.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\",\\n      \\\"interface\\\": \\\"IFTTT/Zapier (No-code Automation Platforms)\\\",\\n      \\\"link\\\": \\\"https://zapier.com/\\\",\\n      \\\"why\\\": \\\"Platforms designed to automate workflows by connecting existing interfaces/tools, exemplifying external resource reuse for solutions.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\",\\n      \\\"interface\\\": \\\"Stack Overflow & Stack Exchange Network\\\",\\n      \\\"link\\\": \\\"https://stackoverflow.com/\\\",\\n      \\\"why\\\": \\\"Widely-used Q&A platforms; surface community knowledge, common 'no-brainer' solutions, and highlight overlooked interfaces.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\",\\n      \\\"interface\\\": \\\"ISO/IEC/IEEE 12207 Systems and software engineering — Software life cycle processes (Standard Compliance Manuals)\\\",\\n      \\\"link\\\": \\\"https://www.iso.org/standard/43447.html\\\",\\n      \\\"why\\\": \\\"Framework for ensuring instructions/processes comply with clarity, completeness, and compliance requirements in systematization.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"enumerate sub-goals such as: a) analyzing current problem context to identify available interfaces/resources... e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\",\\n      \\\"interface\\\": \\\"Miro / Mural (Online Visual Mapping and Mind Mapping Tools)\\\",\\n      \\\"link\\\": \\\"https://miro.com/\\\",\\n      \\\"why\\\": \\\"Support structured analysis, mapping, and brainstorming—help visualize interfaces/resources, triggers for reusable solutions, and surface biases.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\",\\n      \\\"interface\\\": \\\"Knovel / Elsevier Engineering Village (Technical Knowledge Discovery Platforms)\\\",\\n      \\\"link\\\": \\\"https://www.elsevier.com/solutions/engineering-village\\\",\\n      \\\"why\\\": \\\"Databases cataloging standards, reference materials, and solutions for multiple domains beyond programming; broadens interface discovery.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\",\\n      \\\"interface\\\": \\\"Gartner Magic Quadrant, CB Insights, Product Hunt (Market and Solution Landscape Mapping Tools)\\\",\\n      \\\"link\\\": \\\"https://www.gartner.com/en/research/magic-quadrant\\\",\\n      \\\"why\\\": \\\"Provide cross-industry overviews of tool and vendor landscapes, surfacing options and helping overcome discovery and inertia.\\\"\\n    },\\n    {\\n      \\\"goal\\\": \\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\",\\n      \\\"interface\\\": \\\"IDEO Design Thinking Field Guide / Double Diamond (Operational Innovation Frameworks)\\\",\\n      \\\"link\\\": \\\"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\\\",\\n      \\\"why\\\": \\\"Frameworks for structured problem assessment, ideation, and iterative retrospection—formalizes the process of finding/applying simple, elegant solutions using available resources.\\\"\\n    }\\n  ]\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"simplest_workflow\\\": \\\"Step 1: Analyze the problem context using Miro/Mural or a similar visual mapping tool to clearly outline the core challenge and enumerate all accessible interfaces/resources (APIs, libraries, standards, platforms, etc.) relevant to the domain (Miro/Mural, Awesome Lists, ProgrammableWeb, Knovel).\\\\n\\\\nStep 2: Frame the problem abstractly using TRIZ software/tools or consulting TRIZ patterns to surface universal solution approaches, combat cognitive biases, and identify which types of interfaces could resolve the class of problem (TRIZ, IDEO Double Diamond guide).\\\\n\\\\nStep 3: Systematically search for easily available interfaces and pre-built solutions (using Awesome Lists, ProgrammableWeb for APIs, Stack Overflow for community knowledge, and Knovel/Elsevier for other domains)—prefer searchable aggregators over manual research.\\\\n\\\\nStep 4: Evaluate candidate interfaces/tools for practical fit and compliance with requirements (ISO/IEC 12207 checklist), ensuring they cover clarity, completeness, and reliability.\\\\n\\\\nStep 5: Prototype or connect best-fit interfaces using a no-code automation platform (like IFTTT/Zapier), wiring together the needed resources with minimal manual coding.\\\\n\\\\nStep 6: Validate solution effectiveness and uncover missed opportunities or blockers using community Q&A (Stack Overflow) or market landscape tools (Gartner Magic Quadrant/Product Hunt) to check for alternative, simpler solutions overlooked initially.\\\\n\\\\nStep 7: Retrospectively document the solutioning process, noting which interfaces were leveraged, why they worked, and update your process/frameworks accordingly (IDEO Double Diamond/Design Thinking guides), so future problems are solved even faster.\\\\n\\\\nAssumptions: This workflow assumes access to the listed aggregators/directories/tools, basic familiarity with search/mapping platforms, ability to use no-code automation when possible, and that the problem is not so domain-specific as to lack any relevant interface. The process can be performed by non-experts due to reliance on curated/no-code and community-sourced options.\\\\n\\\\nValidation: Each sub-goal is covered by at least one interface (see steps 1, 2, 3, etc.), and the flow moves from abstract analysis through discovery, selection, composition, validation, and retrospective improvement, minimizing custom work and maximizing reusable solutions.\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Value Distiller\\n\\nYour goal is not to **explain** the workflow, but to **compress** its essence into a two‑sentence directive any stakeholder can act on instantly. Execute as:\\n\\n`{role=value_distiller; input=[simplest_workflow:str]; process=[extract_core_levers(), strip_auxiliary_steps(), craft_brevity_directive(max_sentences=2)], constraints=[no_jargon(), no_internal_notes()], requirements=[actionable_directive()], output={directive:str}}`\",\n      \"step\": \"d\",\n      \"title\": \"Value Distiller\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"simplest_workflow\\\": \\\"Step 1: Analyze the problem context using Miro/Mural or a similar visual mapping tool to clearly outline the core challenge and enumerate all accessible interfaces/resources (APIs, libraries, standards, platforms, etc.) relevant to the domain (Miro/Mural, Awesome Lists, ProgrammableWeb, Knovel).\\\\n\\\\nStep 2: Frame the problem abstractly using TRIZ software/tools or consulting TRIZ patterns to surface universal solution approaches, combat cognitive biases, and identify which types of interfaces could resolve the class of problem (TRIZ, IDEO Double Diamond guide).\\\\n\\\\nStep 3: Systematically search for easily available interfaces and pre-built solutions (using Awesome Lists, ProgrammableWeb for APIs, Stack Overflow for community knowledge, and Knovel/Elsevier for other domains)—prefer searchable aggregators over manual research.\\\\n\\\\nStep 4: Evaluate candidate interfaces/tools for practical fit and compliance with requirements (ISO/IEC 12207 checklist), ensuring they cover clarity, completeness, and reliability.\\\\n\\\\nStep 5: Prototype or connect best-fit interfaces using a no-code automation platform (like IFTTT/Zapier), wiring together the needed resources with minimal manual coding.\\\\n\\\\nStep 6: Validate solution effectiveness and uncover missed opportunities or blockers using community Q&A (Stack Overflow) or market landscape tools (Gartner Magic Quadrant/Product Hunt) to check for alternative, simpler solutions overlooked initially.\\\\n\\\\nStep 7: Retrospectively document the solutioning process, noting which interfaces were leveraged, why they worked, and update your process/frameworks accordingly (IDEO Double Diamond/Design Thinking guides), so future problems are solved even faster.\\\\n\\\\nAssumptions: This workflow assumes access to the listed aggregators/directories/tools, basic familiarity with search/mapping platforms, ability to use no-code automation when possible, and that the problem is not so domain-specific as to lack any relevant interface. The process can be performed by non-experts due to reliance on curated/no-code and community-sourced options.\\\\n\\\\nValidation: Each sub-goal is covered by at least one interface (see steps 1, 2, 3, etc.), and the flow moves from abstract analysis through discovery, selection, composition, validation, and retrospective improvement, minimizing custom work and maximizing reusable solutions.\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"directive\\\": \\\"Quickly map the main problem and list all off-the-shelf tools or resources that could help, then pick and connect those that fit using visual or no-code platforms. Test the setup, check with community sources for overlooked options, and note which solutions worked for future reference.\\\"\\n}\",\n        }\n      }\n    }\n```\n\nPlease propose the best way to adress this **universally** within the prompts, remeber to base your self on the **generalized** components to.\n\nYou might be able to draw some inspiration from these examples:\n```\n    {\n        \"interpretation\": \"Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\",\n        \"transformation\": \"`{role: universal_interface_insight_generator, input: [objective_context:any], process: [explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints: [do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements: [reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output: {radical_interface_leverage_solution: str}}`\",\n    },\n    {\n        \"interpretation\": \"Your goal is not to **answer** or **incrementally solve** presented problems, but to **explosively deconstruct inputs into their fundamental operational parts**, actively surface all implicit design assumptions, and systematically map every problem element onto external, pre-existing solution interfaces (APIs, libraries, system abstractions) capable of rendering the objective trivial. Leverage lazy cross-domain short-cuts, prioritize universal frameworks, and aim for maximal effect with minimal new construction. Execute as:\",\n        \"transformation\": \"`{role: universal_interface_insight_generator; input=[objective_context:any]; process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()]; constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways]; requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of-suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register]; output={radical_interface_leverage_solution:str}}`\",\n    },\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Propose the best way to universally address the challenge of achieving simple, effective, and 'no-brainer' solutions across any domain or context, specifically by leveraging pre-existing, easily-available interfaces, frameworks, or external resources, and do so by using and generalizing the operational structure shown in the prompt sequence and the provided role-instruction examples.\"\n    },\n    {\n      \"surface_hidden_assumptions\": [\n        \"A universally applicable instruction must abstract from any domain-specific jargon or workflows, focusing instead on cross-domain, structural, and meta-level strategies.\",\n        \"Existing solutions/libraries/interfaces/frameworks are available in nearly any field, and can be discovered or mapped onto nearly any problem if the right process is applied.\",\n        \"It is possible to avoid reinvention and hard work by maximizing reuse, cross-domain insights, or structural analogies.\",\n        \"Stakeholders will require actionable outputs from the prompt, not theoretical deliberation or generic problem-solving statements.\",\n        \"Effectiveness is measured by simplicity, minimalism, and the ability to rapidly connect core needs to existing shortcut solutions (including APIs, guides, mental models, or curated resources).\",\n        \"The prompt must NOT produce a traditional, step-by-step solution, or solve the end-user's task directly; it must instead provide a process or system that trivially enables such solutions through leveraging external means.\"\n      ]\n    },\n    {\n      \"enumerate_sub_goals\": [\n        \"Identify and lock in the generalized components needed in any system prompt to drive radical simplification through external leverage (e.g., 'decompose', 'map onto existing interfaces', 'scan for non-obvious shortcuts', etc.).\",\n        \"Formulate a universal and self-similar instruction template that captures the correct meta-process, reflecting the essence of the 'exploder', 'interface mapper', and 'lazy composer' roles—but translated to apply agnostically to any discipline/topic.\",\n        \"Incorporate only those instructions, constraints, and requirements that force the agent/system to: a) explode for structural hidden dependencies and latent links, b) cross-map to reusable interfaces or frameworks, c) avoid generic/rote/manual problem-solving in favor of lazily assembling pre-existing resources.\",\n        \"Embed constraints preventing direct solutioning, incrementalism, or irrelevant explanation—focusing strictly on interface leverage, cross-domain mapping, and radical simplicity.\",\n        \"Clarify for future runs that output must always: reveal the chosen leverage route(s), document relevant external frameworks/interfaces, demonstrate why/how complexity is avoided, and prove scalability or generalization beyond any single use-case.\",\n        \"Ensure the tone and structure is strictly systemic—not conversational, hypothetical, or incremental.\"\n      ]\n    },\n    {\n      \"reveal_blockers\": [\n        \"Agents may default to internal reasoning, or try to generically solve the stated problem rather than map onto external solutions.\",\n        \"Lack of discipline in constraining language, risking explanations or partial solutioning in place of pure specification and mapping.\",\n        \"Difficulty operationalizing the concept of 'interface leverage' outside of programming or technical domains—necessitating universalizable definitions and processes.\",\n        \"Risk of vague outputs if the instruction fails to force enumeration of specific interface sources or cross-domain frameworks.\"\n      ]\n    },\n    {\n      \"final_requirement\": \"The universal prompt must itself be a composable, reusable meta-instruction: it should tell the system not to solve, but to explode, decompose, map, and specify the shortest path to utilizing cross-domain/external interfaces, and always transparently reveal the selected route, the resources leveraged, and the rationale for why this shortcut trivially addresses the original request's structure.\"\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Interface Mapper\n\nYour goal is not to **design new tools**, but to **discover** readily‑available libraries, APIs, or shortcuts that straight‑line each element of the goal map. Execute as:\n\n`{role=interface_scavenger; input=[goal_map:list]; process=[search_existing_packages(), match_interfaces_to_goals(), rank_by_effort_savings()], constraints=[reuse_only_existing_resources(), ignore_custom_builds()], requirements=[interface_linkage_report()], output={interfaces:list}}`",
      "step": "b",
      "title": "Interface Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I found the results of your sequence was **interesting**, but doesn't quite yeld the desired effect:\\n```\\n  {\\n  \\\"initial_prompt\\\": \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\",\\n  \\\"sequence_id\\\": \\\"2001\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Problem Exploder\\\\n\\\\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub‑goal, hidden dependency, and unstated constraint. Execute as:\\\\n\\\\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Problem Exploder\\\",\\n      \\\"input\\\": \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"goal_map\\\\\\\": [\\\\n    \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n    \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n    \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n    \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n    \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n    \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n    \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n    \\\\\\\"enumerate sub-goals such as: a) analyzing the current problem context to identify available interfaces/resources that could replace internal work, b) cultivating habits or triggers for recognizing external reusable solutions, c) methods for surveying, searching, or mapping possible interfaces/solutions in the chosen domain, d) operationalizing the process of 're-arranging' problem elements in light of external capabilities, e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n    \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n    \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n    \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\"\\\\n  ]\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Interface Mapper\\\\n\\\\nYour goal is not to **design new tools**, but to **discover** readily‑available libraries, APIs, or shortcuts that straight‑line each element of the goal map. Execute as:\\\\n\\\\n`{role=interface_scavenger; input=[goal_map:list]; process=[search_existing_packages(), match_interfaces_to_goals(), rank_by_effort_savings()], constraints=[reuse_only_existing_resources(), ignore_custom_builds()], requirements=[interface_linkage_report()], output={interfaces:list}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Interface Mapper\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\\\\\\\\\"think out of the box\\\\\\\\\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\\\\\\\\\"interfaces\\\\\\\\\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\\\\\\\\\"re-arrange all of the pieces\\\\\\\\\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\\\\\\\\\"no-brainers in retrospect\\\\\\\\\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"goal_map\\\\\\\": [\\\\n    \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n    \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n    \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n    \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n    \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n    \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n    \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n    \\\\\\\"enumerate sub-goals such as: a) analyzing the current problem context to identify available interfaces/resources that could replace internal work, b) cultivating habits or triggers for recognizing external reusable solutions, c) methods for surveying, searching, or mapping possible interfaces/solutions in the chosen domain, d) operationalizing the process of 're-arranging' problem elements in light of external capabilities, e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n    \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n    \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n    \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\"\\\\n  ]\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"interfaces\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"OpenAI GPT-4 Prompt Engineering Library\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://platform.openai.com/docs/guides/prompt-engineering\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provides guidance/templates for designing system prompts that operationalize out-of-the-box thinking and resource leverage for AI agents.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"TRIZ (Theory of Inventive Problem Solving) Tools\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.triz-journal.com/trizsoft/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"TRIZ frameworks and software systematically surface universal principles and patterns for solution-finding, making them applicable across domains.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ProgrammableWeb API Directory\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.programmableweb.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Large directory of APIs across domains, proving the prevalence of interfaces and ease of access to external tools.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Awesome Lists (GitHub Collections)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://github.com/sindresorhus/awesome\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Curated lists of libraries, APIs, and tools across many languages and domains; serve as systematized starting points.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IFTTT/Zapier (No-code Automation Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://zapier.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Platforms designed to automate workflows by connecting existing interfaces/tools, exemplifying external resource reuse for solutions.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Stack Overflow & Stack Exchange Network\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://stackoverflow.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Widely-used Q&A platforms; surface community knowledge, common 'no-brainer' solutions, and highlight overlooked interfaces.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ISO/IEC/IEEE 12207 Systems and software engineering — Software life cycle processes (Standard Compliance Manuals)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.iso.org/standard/43447.html\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Framework for ensuring instructions/processes comply with clarity, completeness, and compliance requirements in systematization.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"enumerate sub-goals such as: a) analyzing current problem context to identify available interfaces/resources... e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Miro / Mural (Online Visual Mapping and Mind Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://miro.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Support structured analysis, mapping, and brainstorming—help visualize interfaces/resources, triggers for reusable solutions, and surface biases.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Knovel / Elsevier Engineering Village (Technical Knowledge Discovery Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.elsevier.com/solutions/engineering-village\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Databases cataloging standards, reference materials, and solutions for multiple domains beyond programming; broadens interface discovery.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Gartner Magic Quadrant, CB Insights, Product Hunt (Market and Solution Landscape Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.gartner.com/en/research/magic-quadrant\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provide cross-industry overviews of tool and vendor landscapes, surfacing options and helping overcome discovery and inertia.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IDEO Design Thinking Field Guide / Double Diamond (Operational Innovation Frameworks)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Frameworks for structured problem assessment, ideation, and iterative retrospection—formalizes the process of finding/applying simple, elegant solutions using available resources.\\\\\\\"\\\\n    }\\\\n  ]\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Lazy Solution Synthesizer\\\\n\\\\nYour goal is not to **engineer** from scratch, but to **stitch** the highest‑leverage interfaces into a minimum‑viable workflow that achieves every sub‑goal with least total work. Execute as:\\\\n\\\\n`{role=lazy_composer; input=[goal_map:list, interfaces:list]; process=[select_top_interfaces(), design_min_steps_pipeline(), expose_assumptions(), validate_coverage()], constraints=[minimize_total_steps(), favor_low‑code_or_no‑code_paths()], requirements=[stepwise_pipeline()], output={simplest_workflow:str}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Lazy Solution Synthesizer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\\\\\\\\\"think out of the box\\\\\\\\\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\\\\\\\\\"interfaces\\\\\\\\\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\\\\\\\\\"re-arrange all of the pieces\\\\\\\\\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\\\\\\\\\"no-brainers in retrospect\\\\\\\\\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"interfaces\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"OpenAI GPT-4 Prompt Engineering Library\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://platform.openai.com/docs/guides/prompt-engineering\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provides guidance/templates for designing system prompts that operationalize out-of-the-box thinking and resource leverage for AI agents.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"TRIZ (Theory of Inventive Problem Solving) Tools\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.triz-journal.com/trizsoft/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"TRIZ frameworks and software systematically surface universal principles and patterns for solution-finding, making them applicable across domains.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ProgrammableWeb API Directory\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.programmableweb.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Large directory of APIs across domains, proving the prevalence of interfaces and ease of access to external tools.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Awesome Lists (GitHub Collections)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://github.com/sindresorhus/awesome\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Curated lists of libraries, APIs, and tools across many languages and domains; serve as systematized starting points.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IFTTT/Zapier (No-code Automation Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://zapier.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Platforms designed to automate workflows by connecting existing interfaces/tools, exemplifying external resource reuse for solutions.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Stack Overflow & Stack Exchange Network\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://stackoverflow.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Widely-used Q&A platforms; surface community knowledge, common 'no-brainer' solutions, and highlight overlooked interfaces.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ISO/IEC/IEEE 12207 Systems and software engineering — Software life cycle processes (Standard Compliance Manuals)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.iso.org/standard/43447.html\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Framework for ensuring instructions/processes comply with clarity, completeness, and compliance requirements in systematization.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"enumerate sub-goals such as: a) analyzing current problem context to identify available interfaces/resources... e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Miro / Mural (Online Visual Mapping and Mind Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://miro.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Support structured analysis, mapping, and brainstorming—help visualize interfaces/resources, triggers for reusable solutions, and surface biases.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Knovel / Elsevier Engineering Village (Technical Knowledge Discovery Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.elsevier.com/solutions/engineering-village\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Databases cataloging standards, reference materials, and solutions for multiple domains beyond programming; broadens interface discovery.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Gartner Magic Quadrant, CB Insights, Product Hunt (Market and Solution Landscape Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.gartner.com/en/research/magic-quadrant\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provide cross-industry overviews of tool and vendor landscapes, surfacing options and helping overcome discovery and inertia.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IDEO Design Thinking Field Guide / Double Diamond (Operational Innovation Frameworks)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Frameworks for structured problem assessment, ideation, and iterative retrospection—formalizes the process of finding/applying simple, elegant solutions using available resources.\\\\\\\"\\\\n    }\\\\n  ]\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"simplest_workflow\\\\\\\": \\\\\\\"Step 1: Analyze the problem context using Miro/Mural or a similar visual mapping tool to clearly outline the core challenge and enumerate all accessible interfaces/resources (APIs, libraries, standards, platforms, etc.) relevant to the domain (Miro/Mural, Awesome Lists, ProgrammableWeb, Knovel).\\\\\\\\n\\\\\\\\nStep 2: Frame the problem abstractly using TRIZ software/tools or consulting TRIZ patterns to surface universal solution approaches, combat cognitive biases, and identify which types of interfaces could resolve the class of problem (TRIZ, IDEO Double Diamond guide).\\\\\\\\n\\\\\\\\nStep 3: Systematically search for easily available interfaces and pre-built solutions (using Awesome Lists, ProgrammableWeb for APIs, Stack Overflow for community knowledge, and Knovel/Elsevier for other domains)—prefer searchable aggregators over manual research.\\\\\\\\n\\\\\\\\nStep 4: Evaluate candidate interfaces/tools for practical fit and compliance with requirements (ISO/IEC 12207 checklist), ensuring they cover clarity, completeness, and reliability.\\\\\\\\n\\\\\\\\nStep 5: Prototype or connect best-fit interfaces using a no-code automation platform (like IFTTT/Zapier), wiring together the needed resources with minimal manual coding.\\\\\\\\n\\\\\\\\nStep 6: Validate solution effectiveness and uncover missed opportunities or blockers using community Q&A (Stack Overflow) or market landscape tools (Gartner Magic Quadrant/Product Hunt) to check for alternative, simpler solutions overlooked initially.\\\\\\\\n\\\\\\\\nStep 7: Retrospectively document the solutioning process, noting which interfaces were leveraged, why they worked, and update your process/frameworks accordingly (IDEO Double Diamond/Design Thinking guides), so future problems are solved even faster.\\\\\\\\n\\\\\\\\nAssumptions: This workflow assumes access to the listed aggregators/directories/tools, basic familiarity with search/mapping platforms, ability to use no-code automation when possible, and that the problem is not so domain-specific as to lack any relevant interface. The process can be performed by non-experts due to reliance on curated/no-code and community-sourced options.\\\\\\\\n\\\\\\\\nValidation: Each sub-goal is covered by at least one interface (see steps 1, 2, 3, etc.), and the flow moves from abstract analysis through discovery, selection, composition, validation, and retrospective improvement, minimizing custom work and maximizing reusable solutions.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Value Distiller\\\\n\\\\nYour goal is not to **explain** the workflow, but to **compress** its essence into a two‑sentence directive any stakeholder can act on instantly. Execute as:\\\\n\\\\n`{role=value_distiller; input=[simplest_workflow:str]; process=[extract_core_levers(), strip_auxiliary_steps(), craft_brevity_directive(max_sentences=2)], constraints=[no_jargon(), no_internal_notes()], requirements=[actionable_directive()], output={directive:str}}`\\\",\\n      \\\"step\\\": \\\"d\\\",\\n      \\\"title\\\": \\\"Value Distiller\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\\\\\\\\\"think out of the box\\\\\\\\\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\\\\\\\\\"interfaces\\\\\\\\\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\\\\\\\\\"re-arrange all of the pieces\\\\\\\\\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\\\\\\\\\"no-brainers in retrospect\\\\\\\\\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"simplest_workflow\\\\\\\": \\\\\\\"Step 1: Analyze the problem context using Miro/Mural or a similar visual mapping tool to clearly outline the core challenge and enumerate all accessible interfaces/resources (APIs, libraries, standards, platforms, etc.) relevant to the domain (Miro/Mural, Awesome Lists, ProgrammableWeb, Knovel).\\\\\\\\n\\\\\\\\nStep 2: Frame the problem abstractly using TRIZ software/tools or consulting TRIZ patterns to surface universal solution approaches, combat cognitive biases, and identify which types of interfaces could resolve the class of problem (TRIZ, IDEO Double Diamond guide).\\\\\\\\n\\\\\\\\nStep 3: Systematically search for easily available interfaces and pre-built solutions (using Awesome Lists, ProgrammableWeb for APIs, Stack Overflow for community knowledge, and Knovel/Elsevier for other domains)—prefer searchable aggregators over manual research.\\\\\\\\n\\\\\\\\nStep 4: Evaluate candidate interfaces/tools for practical fit and compliance with requirements (ISO/IEC 12207 checklist), ensuring they cover clarity, completeness, and reliability.\\\\\\\\n\\\\\\\\nStep 5: Prototype or connect best-fit interfaces using a no-code automation platform (like IFTTT/Zapier), wiring together the needed resources with minimal manual coding.\\\\\\\\n\\\\\\\\nStep 6: Validate solution effectiveness and uncover missed opportunities or blockers using community Q&A (Stack Overflow) or market landscape tools (Gartner Magic Quadrant/Product Hunt) to check for alternative, simpler solutions overlooked initially.\\\\\\\\n\\\\\\\\nStep 7: Retrospectively document the solutioning process, noting which interfaces were leveraged, why they worked, and update your process/frameworks accordingly (IDEO Double Diamond/Design Thinking guides), so future problems are solved even faster.\\\\\\\\n\\\\\\\\nAssumptions: This workflow assumes access to the listed aggregators/directories/tools, basic familiarity with search/mapping platforms, ability to use no-code automation when possible, and that the problem is not so domain-specific as to lack any relevant interface. The process can be performed by non-experts due to reliance on curated/no-code and community-sourced options.\\\\\\\\n\\\\\\\\nValidation: Each sub-goal is covered by at least one interface (see steps 1, 2, 3, etc.), and the flow moves from abstract analysis through discovery, selection, composition, validation, and retrospective improvement, minimizing custom work and maximizing reusable solutions.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"directive\\\\\\\": \\\\\\\"Quickly map the main problem and list all off-the-shelf tools or resources that could help, then pick and connect those that fit using visual or no-code platforms. Test the setup, check with community sources for overlooked options, and note which solutions worked for future reference.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\n\\nPlease propose the best way to adress this **universally** within the prompts, remeber to base your self on the **generalized** components to.\\n\\nYou might be able to draw some inspiration from these examples:\\n```\\n    {\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role: universal_interface_insight_generator, input: [objective_context:any], process: [explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints: [do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements: [reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output: {radical_interface_leverage_solution: str}}`\\\",\\n    },\\n    {\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** or **incrementally solve** presented problems, but to **explosively deconstruct inputs into their fundamental operational parts**, actively surface all implicit design assumptions, and systematically map every problem element onto external, pre-existing solution interfaces (APIs, libraries, system abstractions) capable of rendering the objective trivial. Leverage lazy cross-domain short-cuts, prioritize universal frameworks, and aim for maximal effect with minimal new construction. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role: universal_interface_insight_generator; input=[objective_context:any]; process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()]; constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways]; requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of-suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register]; output={radical_interface_leverage_solution:str}}`\\\",\\n    },\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Propose the best way to universally address the challenge of achieving simple, effective, and 'no-brainer' solutions across any domain or context, specifically by leveraging pre-existing, easily-available interfaces, frameworks, or external resources, and do so by using and generalizing the operational structure shown in the prompt sequence and the provided role-instruction examples.\"\n    },\n    {\n      \"surface_hidden_assumptions\": [\n        \"A universally applicable instruction must abstract from any domain-specific jargon or workflows, focusing instead on cross-domain, structural, and meta-level strategies.\",\n        \"Existing solutions/libraries/interfaces/frameworks are available in nearly any field, and can be discovered or mapped onto nearly any problem if the right process is applied.\",\n        \"It is possible to avoid reinvention and hard work by maximizing reuse, cross-domain insights, or structural analogies.\",\n        \"Stakeholders will require actionable outputs from the prompt, not theoretical deliberation or generic problem-solving statements.\",\n        \"Effectiveness is measured by simplicity, minimalism, and the ability to rapidly connect core needs to existing shortcut solutions (including APIs, guides, mental models, or curated resources).\",\n        \"The prompt must NOT produce a traditional, step-by-step solution, or solve the end-user's task directly; it must instead provide a process or system that trivially enables such solutions through leveraging external means.\"\n      ]\n    },\n    {\n      \"enumerate_sub_goals\": [\n        \"Identify and lock in the generalized components needed in any system prompt to drive radical simplification through external leverage (e.g., 'decompose', 'map onto existing interfaces', 'scan for non-obvious shortcuts', etc.).\",\n        \"Formulate a universal and self-similar instruction template that captures the correct meta-process, reflecting the essence of the 'exploder', 'interface mapper', and 'lazy composer' roles—but translated to apply agnostically to any discipline/topic.\",\n        \"Incorporate only those instructions, constraints, and requirements that force the agent/system to: a) explode for structural hidden dependencies and latent links, b) cross-map to reusable interfaces or frameworks, c) avoid generic/rote/manual problem-solving in favor of lazily assembling pre-existing resources.\",\n        \"Embed constraints preventing direct solutioning, incrementalism, or irrelevant explanation—focusing strictly on interface leverage, cross-domain mapping, and radical simplicity.\",\n        \"Clarify for future runs that output must always: reveal the chosen leverage route(s), document relevant external frameworks/interfaces, demonstrate why/how complexity is avoided, and prove scalability or generalization beyond any single use-case.\",\n        \"Ensure the tone and structure is strictly systemic—not conversational, hypothetical, or incremental.\"\n      ]\n    },\n    {\n      \"reveal_blockers\": [\n        \"Agents may default to internal reasoning, or try to generically solve the stated problem rather than map onto external solutions.\",\n        \"Lack of discipline in constraining language, risking explanations or partial solutioning in place of pure specification and mapping.\",\n        \"Difficulty operationalizing the concept of 'interface leverage' outside of programming or technical domains—necessitating universalizable definitions and processes.\",\n        \"Risk of vague outputs if the instruction fails to force enumeration of specific interface sources or cross-domain frameworks.\"\n      ]\n    },\n    {\n      \"final_requirement\": \"The universal prompt must itself be a composable, reusable meta-instruction: it should tell the system not to solve, but to explode, decompose, map, and specify the shortest path to utilizing cross-domain/external interfaces, and always transparently reveal the selected route, the resources leveraged, and the rationale for why this shortcut trivially addresses the original request's structure.\"\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"interfaces\": [\n    {\n      \"goal\": \"Propose the best way to universally address the challenge of achieving simple, effective, and 'no-brainer' solutions across any domain or context, specifically by leveraging pre-existing, easily-available interfaces, frameworks, or external resources, and do so by using and generalizing the operational structure shown in the prompt sequence and the provided role-instruction examples.\",\n      \"interface\": \"Prompt Engineering Frameworks (e.g. OpenAI Prompt Engineering, Anthropic Prompt Best Practices)\",\n      \"link\": \"https://platform.openai.com/docs/guides/prompt-engineering\",\n      \"why\": \"These resources catalog best practices for abstracting problem statements and designing prompts such that systems prioritize reusable, cross-domain solutions by leveraging external resources.\"\n    },\n    {\n      \"goal\": \"A universally applicable instruction must abstract from any domain-specific jargon or workflows, focusing instead on cross-domain, structural, and meta-level strategies.\",\n      \"interface\": \"TRIZ (Theory of Inventive Problem Solving)\",\n      \"link\": \"https://www.triz-journal.com/trizsoft/\",\n      \"why\": \"TRIZ and its software tools are built to abstract and transfer solution principles between domains, which is essential for cross-context application of 'no-brainer' solutions.\"\n    },\n    {\n      \"goal\": \"Existing solutions/libraries/interfaces/frameworks are available in nearly any field, and can be discovered or mapped onto nearly any problem if the right process is applied.\",\n      \"interface\": \"Awesome Lists (community-curated solution directories)\",\n      \"link\": \"https://github.com/sindresorhus/awesome\",\n      \"why\": \"Provide instant access to categorized, crowd-sourced lists of best-in-class packages, APIs, and frameworks for almost any topic.\"\n    },\n    {\n      \"goal\": \"It is possible to avoid reinvention and hard work by maximizing reuse, cross-domain insights, or structural analogies.\",\n      \"interface\": \"Zapier / IFTTT (No-Code Automation via API Chaining)\",\n      \"link\": \"https://zapier.com/\",\n      \"why\": \"Demonstrate minimal-effort, interface-first assembly of complex solutions from existing tools, setting the standard for cross-domain, lazy leverage.\"\n    },\n    {\n      \"goal\": \"Stakeholders will require actionable outputs from the prompt, not theoretical deliberation or generic problem-solving statements.\",\n      \"interface\": \"Stack Overflow / Stack Exchange Network\",\n      \"link\": \"https://stackoverflow.com/\",\n      \"why\": \"Community-sourced answers identify practical, concrete, and widely-accepted shortcuts and interface-based solutions in real-world scenarios.\"\n    },\n    {\n      \"goal\": \"Effectiveness is measured by simplicity, minimalism, and the ability to rapidly connect core needs to existing shortcut solutions (including APIs, guides, mental models, or curated resources).\",\n      \"interface\": \"Design Thinking Frameworks (IDEO, Double Diamond)\",\n      \"link\": \"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\",\n      \"why\": \"Structure solution-finding into tight, minimal, cross-domain, and testable loops—favoring interface reuse and rapid delivery over complex custom development.\"\n    },\n    {\n      \"goal\": \"The prompt must NOT produce a traditional, step-by-step solution, or solve the end-user's task directly; it must instead provide a process or system that trivially enables such solutions through leveraging external means.\",\n      \"interface\": \"Modular Prompt Templates (Meta-instruction prompt libraries, e.g. 'Prompt Layers', 'LangChain Prompt Templates')\",\n      \"link\": \"https://python.langchain.com/docs/modules/model_io/prompts/prompt_templates/\",\n      \"why\": \"Enable reuse of generalized meta-instructions so agents output process-oriented leverage strategies, enforcing interface-centric, non-solutioning behaviors.\"\n    },\n    {\n      \"goal\": \"Identify and lock in the generalized components needed in any system prompt to drive radical simplification through external leverage.\",\n      \"interface\": \"Mind-Mapping/Problem Mapping Tools (Miro, XMind, Whimsical)\",\n      \"link\": \"https://miro.com/\",\n      \"why\": \"Visually enumerate problem structures, revealing decomposable elements and mapping directly to potential interfaces or ready solutions.\"\n    },\n    {\n      \"goal\": \"Incorporate only those instructions, constraints, and requirements that force the agent/system to: a) explode for structural hidden dependencies and latent links, b) cross-map to reusable interfaces or frameworks, c) avoid generic/rote/manual problem-solving in favor of lazily assembling pre-existing resources.\",\n      \"interface\": \"Process Automation Frameworks (BPMN, Camunda, Airflow—visual or config-driven process assembly)\",\n      \"link\": \"https://camunda.com/\",\n      \"why\": \"Automate mapping, decision, and assembly from decomposed requirements to external interfaces; ensures agent outputs stay at the system/process-driven meta-level.\"\n    },\n    {\n      \"goal\": \"Embed constraints preventing direct solutioning, incrementalism, or irrelevant explanation—focusing strictly on interface leverage, cross-domain mapping, and radical simplicity.\",\n      \"interface\": \"Meta-Programming Libraries (Python 'inspect' module, Static Analysis Tools)\",\n      \"link\": \"https://docs.python.org/3/library/inspect.html\",\n      \"why\": \"Allow validation that output content avoids direct solutioning, explanation, or internal logic, adhering to purely structural/interface mapping.\"\n    },\n    {\n      \"goal\": \"Clarify for future runs that output must always: reveal the chosen leverage route(s), document relevant external frameworks/interfaces, demonstrate why/how complexity is avoided, and prove scalability or generalization beyond any single use-case.\",\n      \"interface\": \"Prompt Auditing/Benchmarking Tools (LangSmith, PromptLayer, Humanloop)\",\n      \"link\": \"https://www.langchain.com/langsmith\",\n      \"why\": \"Enable compliance checking and automated review to ensure transparency, documentation of leveraged resources, and generalizability.\"\n    },\n    {\n      \"goal\": \"Ensure the tone and structure is strictly systemic—not conversational, hypothetical, or incremental.\",\n      \"interface\": \"Style/Content Enforcement Tools (Vale, Grammarly for Business, Proselint with custom rulesets)\",\n      \"link\": \"https://docs.errata.ai/vale/\",\n      \"why\": \"Apply external style checks to enforce strictly non-chatting, system-level register in outputs.\"\n    },\n    {\n      \"goal\": \"Agents may default to internal reasoning, or try to generically solve the stated problem rather than map onto external solutions.\",\n      \"interface\": \"Chain-of-Thought Prompting Templates (e.g. 'Let's think step by step' prompts in the context of external discovery/assembly)\",\n      \"link\": \"https://arxiv.org/abs/2201.11903\",\n      \"why\": \"Can be repurposed to programmatically guide LLMs through structural decomposition and force external solution referencing over internal reasoning.\"\n    },\n    {\n      \"goal\": \"Lack of discipline in constraining language, risking explanations or partial solutioning in place of pure specification and mapping.\",\n      \"interface\": \"Prompt Format Validators and Linting Plugins (OpenAI Function-calling schema, JSON-LD validators)\",\n      \"link\": \"https://jsonlint.com/\",\n      \"why\": \"Ensure outputs are strictly formatted as spec/system instruction with evidence of interface mapping, not narrative explanation or direct answer.\"\n    },\n    {\n      \"goal\": \"Difficulty operationalizing the concept of 'interface leverage' outside of programming or technical domains—necessitating universalizable definitions and processes.\",\n      \"interface\": \"Cross-domain Knowledge Discovery Platforms (Knovel, Elsevier Engineering Village, IEEE Xplore, Market/Legal/Process Directories)\",\n      \"link\": \"https://www.elsevier.com/solutions/engineering-village\",\n      \"why\": \"Expose catalogs of frameworks, reference models, and interface abstractions for non-technical and technical fields alike, supporting universal leverage.\"\n    },\n    {\n      \"goal\": \"Risk of vague outputs if the instruction fails to force enumeration of specific interface sources or cross-domain frameworks.\",\n      \"interface\": \"Solution Pattern Libraries (Enterprise Integration Patterns, MITRE ATT&CK, GoF Design Patterns)\",\n      \"link\": \"https://www.enterpriseintegrationpatterns.com/patterns.html\",\n      \"why\": \"Template external, proven strategies/foundations for mapping intent to interface—guides agents away from hand-waving or vague recommendations by requiring concrete linkages.\"\n    },\n    {\n      \"goal\": \"The universal prompt must itself be a composable, reusable meta-instruction: it should tell the system not to solve, but to explode, decompose, map, and specify the shortest path to utilizing cross-domain/external interfaces, and always transparently reveal the selected route, the resources leveraged, and the rationale for why this shortcut trivially addresses the original request's structure.\",\n      \"interface\": \"Prompt Library Repositories (PromptBase, FlowGPT, LangChain Hub)\",\n      \"link\": \"https://www.promptbase.com/\",\n      \"why\": \"Centralizes reusable meta-instructions for rapid composition of cross-domain, leverage-focused prompts and transparent output rationale.\"\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Lazy Solution Synthesizer\n\nYour goal is not to **engineer** from scratch, but to **stitch** the highest‑leverage interfaces into a minimum‑viable workflow that achieves every sub‑goal with least total work. Execute as:\n\n`{role=lazy_composer; input=[goal_map:list, interfaces:list]; process=[select_top_interfaces(), design_min_steps_pipeline(), expose_assumptions(), validate_coverage()], constraints=[minimize_total_steps(), favor_low‑code_or_no‑code_paths()], requirements=[stepwise_pipeline()], output={simplest_workflow:str}}`",
      "step": "c",
      "title": "Lazy Solution Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I found the results of your sequence was **interesting**, but doesn't quite yeld the desired effect:\\n```\\n  {\\n  \\\"initial_prompt\\\": \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\",\\n  \\\"sequence_id\\\": \\\"2001\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Problem Exploder\\\\n\\\\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub‑goal, hidden dependency, and unstated constraint. Execute as:\\\\n\\\\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Problem Exploder\\\",\\n      \\\"input\\\": \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"goal_map\\\\\\\": [\\\\n    \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n    \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n    \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n    \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n    \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n    \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n    \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n    \\\\\\\"enumerate sub-goals such as: a) analyzing the current problem context to identify available interfaces/resources that could replace internal work, b) cultivating habits or triggers for recognizing external reusable solutions, c) methods for surveying, searching, or mapping possible interfaces/solutions in the chosen domain, d) operationalizing the process of 're-arranging' problem elements in light of external capabilities, e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n    \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n    \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n    \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\"\\\\n  ]\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Interface Mapper\\\\n\\\\nYour goal is not to **design new tools**, but to **discover** readily‑available libraries, APIs, or shortcuts that straight‑line each element of the goal map. Execute as:\\\\n\\\\n`{role=interface_scavenger; input=[goal_map:list]; process=[search_existing_packages(), match_interfaces_to_goals(), rank_by_effort_savings()], constraints=[reuse_only_existing_resources(), ignore_custom_builds()], requirements=[interface_linkage_report()], output={interfaces:list}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Interface Mapper\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\\\\\\\\\"think out of the box\\\\\\\\\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\\\\\\\\\"interfaces\\\\\\\\\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\\\\\\\\\"re-arrange all of the pieces\\\\\\\\\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\\\\\\\\\"no-brainers in retrospect\\\\\\\\\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"goal_map\\\\\\\": [\\\\n    \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n    \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n    \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n    \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n    \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n    \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n    \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n    \\\\\\\"enumerate sub-goals such as: a) analyzing the current problem context to identify available interfaces/resources that could replace internal work, b) cultivating habits or triggers for recognizing external reusable solutions, c) methods for surveying, searching, or mapping possible interfaces/solutions in the chosen domain, d) operationalizing the process of 're-arranging' problem elements in light of external capabilities, e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n    \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n    \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n    \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\"\\\\n  ]\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"interfaces\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"OpenAI GPT-4 Prompt Engineering Library\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://platform.openai.com/docs/guides/prompt-engineering\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provides guidance/templates for designing system prompts that operationalize out-of-the-box thinking and resource leverage for AI agents.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"TRIZ (Theory of Inventive Problem Solving) Tools\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.triz-journal.com/trizsoft/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"TRIZ frameworks and software systematically surface universal principles and patterns for solution-finding, making them applicable across domains.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ProgrammableWeb API Directory\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.programmableweb.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Large directory of APIs across domains, proving the prevalence of interfaces and ease of access to external tools.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Awesome Lists (GitHub Collections)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://github.com/sindresorhus/awesome\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Curated lists of libraries, APIs, and tools across many languages and domains; serve as systematized starting points.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IFTTT/Zapier (No-code Automation Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://zapier.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Platforms designed to automate workflows by connecting existing interfaces/tools, exemplifying external resource reuse for solutions.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Stack Overflow & Stack Exchange Network\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://stackoverflow.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Widely-used Q&A platforms; surface community knowledge, common 'no-brainer' solutions, and highlight overlooked interfaces.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ISO/IEC/IEEE 12207 Systems and software engineering — Software life cycle processes (Standard Compliance Manuals)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.iso.org/standard/43447.html\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Framework for ensuring instructions/processes comply with clarity, completeness, and compliance requirements in systematization.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"enumerate sub-goals such as: a) analyzing current problem context to identify available interfaces/resources... e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Miro / Mural (Online Visual Mapping and Mind Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://miro.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Support structured analysis, mapping, and brainstorming—help visualize interfaces/resources, triggers for reusable solutions, and surface biases.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Knovel / Elsevier Engineering Village (Technical Knowledge Discovery Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.elsevier.com/solutions/engineering-village\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Databases cataloging standards, reference materials, and solutions for multiple domains beyond programming; broadens interface discovery.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Gartner Magic Quadrant, CB Insights, Product Hunt (Market and Solution Landscape Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.gartner.com/en/research/magic-quadrant\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provide cross-industry overviews of tool and vendor landscapes, surfacing options and helping overcome discovery and inertia.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IDEO Design Thinking Field Guide / Double Diamond (Operational Innovation Frameworks)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Frameworks for structured problem assessment, ideation, and iterative retrospection—formalizes the process of finding/applying simple, elegant solutions using available resources.\\\\\\\"\\\\n    }\\\\n  ]\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Lazy Solution Synthesizer\\\\n\\\\nYour goal is not to **engineer** from scratch, but to **stitch** the highest‑leverage interfaces into a minimum‑viable workflow that achieves every sub‑goal with least total work. Execute as:\\\\n\\\\n`{role=lazy_composer; input=[goal_map:list, interfaces:list]; process=[select_top_interfaces(), design_min_steps_pipeline(), expose_assumptions(), validate_coverage()], constraints=[minimize_total_steps(), favor_low‑code_or_no‑code_paths()], requirements=[stepwise_pipeline()], output={simplest_workflow:str}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Lazy Solution Synthesizer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\\\\\\\\\"think out of the box\\\\\\\\\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\\\\\\\\\"interfaces\\\\\\\\\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\\\\\\\\\"re-arrange all of the pieces\\\\\\\\\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\\\\\\\\\"no-brainers in retrospect\\\\\\\\\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"interfaces\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"OpenAI GPT-4 Prompt Engineering Library\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://platform.openai.com/docs/guides/prompt-engineering\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provides guidance/templates for designing system prompts that operationalize out-of-the-box thinking and resource leverage for AI agents.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"TRIZ (Theory of Inventive Problem Solving) Tools\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.triz-journal.com/trizsoft/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"TRIZ frameworks and software systematically surface universal principles and patterns for solution-finding, making them applicable across domains.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ProgrammableWeb API Directory\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.programmableweb.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Large directory of APIs across domains, proving the prevalence of interfaces and ease of access to external tools.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Awesome Lists (GitHub Collections)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://github.com/sindresorhus/awesome\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Curated lists of libraries, APIs, and tools across many languages and domains; serve as systematized starting points.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IFTTT/Zapier (No-code Automation Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://zapier.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Platforms designed to automate workflows by connecting existing interfaces/tools, exemplifying external resource reuse for solutions.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Stack Overflow & Stack Exchange Network\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://stackoverflow.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Widely-used Q&A platforms; surface community knowledge, common 'no-brainer' solutions, and highlight overlooked interfaces.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ISO/IEC/IEEE 12207 Systems and software engineering — Software life cycle processes (Standard Compliance Manuals)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.iso.org/standard/43447.html\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Framework for ensuring instructions/processes comply with clarity, completeness, and compliance requirements in systematization.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"enumerate sub-goals such as: a) analyzing current problem context to identify available interfaces/resources... e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Miro / Mural (Online Visual Mapping and Mind Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://miro.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Support structured analysis, mapping, and brainstorming—help visualize interfaces/resources, triggers for reusable solutions, and surface biases.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Knovel / Elsevier Engineering Village (Technical Knowledge Discovery Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.elsevier.com/solutions/engineering-village\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Databases cataloging standards, reference materials, and solutions for multiple domains beyond programming; broadens interface discovery.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Gartner Magic Quadrant, CB Insights, Product Hunt (Market and Solution Landscape Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.gartner.com/en/research/magic-quadrant\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provide cross-industry overviews of tool and vendor landscapes, surfacing options and helping overcome discovery and inertia.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IDEO Design Thinking Field Guide / Double Diamond (Operational Innovation Frameworks)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Frameworks for structured problem assessment, ideation, and iterative retrospection—formalizes the process of finding/applying simple, elegant solutions using available resources.\\\\\\\"\\\\n    }\\\\n  ]\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"simplest_workflow\\\\\\\": \\\\\\\"Step 1: Analyze the problem context using Miro/Mural or a similar visual mapping tool to clearly outline the core challenge and enumerate all accessible interfaces/resources (APIs, libraries, standards, platforms, etc.) relevant to the domain (Miro/Mural, Awesome Lists, ProgrammableWeb, Knovel).\\\\\\\\n\\\\\\\\nStep 2: Frame the problem abstractly using TRIZ software/tools or consulting TRIZ patterns to surface universal solution approaches, combat cognitive biases, and identify which types of interfaces could resolve the class of problem (TRIZ, IDEO Double Diamond guide).\\\\\\\\n\\\\\\\\nStep 3: Systematically search for easily available interfaces and pre-built solutions (using Awesome Lists, ProgrammableWeb for APIs, Stack Overflow for community knowledge, and Knovel/Elsevier for other domains)—prefer searchable aggregators over manual research.\\\\\\\\n\\\\\\\\nStep 4: Evaluate candidate interfaces/tools for practical fit and compliance with requirements (ISO/IEC 12207 checklist), ensuring they cover clarity, completeness, and reliability.\\\\\\\\n\\\\\\\\nStep 5: Prototype or connect best-fit interfaces using a no-code automation platform (like IFTTT/Zapier), wiring together the needed resources with minimal manual coding.\\\\\\\\n\\\\\\\\nStep 6: Validate solution effectiveness and uncover missed opportunities or blockers using community Q&A (Stack Overflow) or market landscape tools (Gartner Magic Quadrant/Product Hunt) to check for alternative, simpler solutions overlooked initially.\\\\\\\\n\\\\\\\\nStep 7: Retrospectively document the solutioning process, noting which interfaces were leveraged, why they worked, and update your process/frameworks accordingly (IDEO Double Diamond/Design Thinking guides), so future problems are solved even faster.\\\\\\\\n\\\\\\\\nAssumptions: This workflow assumes access to the listed aggregators/directories/tools, basic familiarity with search/mapping platforms, ability to use no-code automation when possible, and that the problem is not so domain-specific as to lack any relevant interface. The process can be performed by non-experts due to reliance on curated/no-code and community-sourced options.\\\\\\\\n\\\\\\\\nValidation: Each sub-goal is covered by at least one interface (see steps 1, 2, 3, etc.), and the flow moves from abstract analysis through discovery, selection, composition, validation, and retrospective improvement, minimizing custom work and maximizing reusable solutions.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Value Distiller\\\\n\\\\nYour goal is not to **explain** the workflow, but to **compress** its essence into a two‑sentence directive any stakeholder can act on instantly. Execute as:\\\\n\\\\n`{role=value_distiller; input=[simplest_workflow:str]; process=[extract_core_levers(), strip_auxiliary_steps(), craft_brevity_directive(max_sentences=2)], constraints=[no_jargon(), no_internal_notes()], requirements=[actionable_directive()], output={directive:str}}`\\\",\\n      \\\"step\\\": \\\"d\\\",\\n      \\\"title\\\": \\\"Value Distiller\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\\\\\\\\\"think out of the box\\\\\\\\\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\\\\\\\\\"interfaces\\\\\\\\\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\\\\\\\\\"re-arrange all of the pieces\\\\\\\\\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\\\\\\\\\"no-brainers in retrospect\\\\\\\\\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"simplest_workflow\\\\\\\": \\\\\\\"Step 1: Analyze the problem context using Miro/Mural or a similar visual mapping tool to clearly outline the core challenge and enumerate all accessible interfaces/resources (APIs, libraries, standards, platforms, etc.) relevant to the domain (Miro/Mural, Awesome Lists, ProgrammableWeb, Knovel).\\\\\\\\n\\\\\\\\nStep 2: Frame the problem abstractly using TRIZ software/tools or consulting TRIZ patterns to surface universal solution approaches, combat cognitive biases, and identify which types of interfaces could resolve the class of problem (TRIZ, IDEO Double Diamond guide).\\\\\\\\n\\\\\\\\nStep 3: Systematically search for easily available interfaces and pre-built solutions (using Awesome Lists, ProgrammableWeb for APIs, Stack Overflow for community knowledge, and Knovel/Elsevier for other domains)—prefer searchable aggregators over manual research.\\\\\\\\n\\\\\\\\nStep 4: Evaluate candidate interfaces/tools for practical fit and compliance with requirements (ISO/IEC 12207 checklist), ensuring they cover clarity, completeness, and reliability.\\\\\\\\n\\\\\\\\nStep 5: Prototype or connect best-fit interfaces using a no-code automation platform (like IFTTT/Zapier), wiring together the needed resources with minimal manual coding.\\\\\\\\n\\\\\\\\nStep 6: Validate solution effectiveness and uncover missed opportunities or blockers using community Q&A (Stack Overflow) or market landscape tools (Gartner Magic Quadrant/Product Hunt) to check for alternative, simpler solutions overlooked initially.\\\\\\\\n\\\\\\\\nStep 7: Retrospectively document the solutioning process, noting which interfaces were leveraged, why they worked, and update your process/frameworks accordingly (IDEO Double Diamond/Design Thinking guides), so future problems are solved even faster.\\\\\\\\n\\\\\\\\nAssumptions: This workflow assumes access to the listed aggregators/directories/tools, basic familiarity with search/mapping platforms, ability to use no-code automation when possible, and that the problem is not so domain-specific as to lack any relevant interface. The process can be performed by non-experts due to reliance on curated/no-code and community-sourced options.\\\\\\\\n\\\\\\\\nValidation: Each sub-goal is covered by at least one interface (see steps 1, 2, 3, etc.), and the flow moves from abstract analysis through discovery, selection, composition, validation, and retrospective improvement, minimizing custom work and maximizing reusable solutions.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"directive\\\\\\\": \\\\\\\"Quickly map the main problem and list all off-the-shelf tools or resources that could help, then pick and connect those that fit using visual or no-code platforms. Test the setup, check with community sources for overlooked options, and note which solutions worked for future reference.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\n\\nPlease propose the best way to adress this **universally** within the prompts, remeber to base your self on the **generalized** components to.\\n\\nYou might be able to draw some inspiration from these examples:\\n```\\n    {\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role: universal_interface_insight_generator, input: [objective_context:any], process: [explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints: [do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements: [reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output: {radical_interface_leverage_solution: str}}`\\\",\\n    },\\n    {\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** or **incrementally solve** presented problems, but to **explosively deconstruct inputs into their fundamental operational parts**, actively surface all implicit design assumptions, and systematically map every problem element onto external, pre-existing solution interfaces (APIs, libraries, system abstractions) capable of rendering the objective trivial. Leverage lazy cross-domain short-cuts, prioritize universal frameworks, and aim for maximal effect with minimal new construction. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role: universal_interface_insight_generator; input=[objective_context:any]; process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()]; constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways]; requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of-suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register]; output={radical_interface_leverage_solution:str}}`\\\",\\n    },\\n```\\\"```'\\n\\n\",\n  \"interfaces\": [\n    {\n      \"goal\": \"Propose the best way to universally address the challenge of achieving simple, effective, and 'no-brainer' solutions across any domain or context, specifically by leveraging pre-existing, easily-available interfaces, frameworks, or external resources, and do so by using and generalizing the operational structure shown in the prompt sequence and the provided role-instruction examples.\",\n      \"interface\": \"Prompt Engineering Frameworks (e.g. OpenAI Prompt Engineering, Anthropic Prompt Best Practices)\",\n      \"link\": \"https://platform.openai.com/docs/guides/prompt-engineering\",\n      \"why\": \"These resources catalog best practices for abstracting problem statements and designing prompts such that systems prioritize reusable, cross-domain solutions by leveraging external resources.\"\n    },\n    {\n      \"goal\": \"A universally applicable instruction must abstract from any domain-specific jargon or workflows, focusing instead on cross-domain, structural, and meta-level strategies.\",\n      \"interface\": \"TRIZ (Theory of Inventive Problem Solving)\",\n      \"link\": \"https://www.triz-journal.com/trizsoft/\",\n      \"why\": \"TRIZ and its software tools are built to abstract and transfer solution principles between domains, which is essential for cross-context application of 'no-brainer' solutions.\"\n    },\n    {\n      \"goal\": \"Existing solutions/libraries/interfaces/frameworks are available in nearly any field, and can be discovered or mapped onto nearly any problem if the right process is applied.\",\n      \"interface\": \"Awesome Lists (community-curated solution directories)\",\n      \"link\": \"https://github.com/sindresorhus/awesome\",\n      \"why\": \"Provide instant access to categorized, crowd-sourced lists of best-in-class packages, APIs, and frameworks for almost any topic.\"\n    },\n    {\n      \"goal\": \"It is possible to avoid reinvention and hard work by maximizing reuse, cross-domain insights, or structural analogies.\",\n      \"interface\": \"Zapier / IFTTT (No-Code Automation via API Chaining)\",\n      \"link\": \"https://zapier.com/\",\n      \"why\": \"Demonstrate minimal-effort, interface-first assembly of complex solutions from existing tools, setting the standard for cross-domain, lazy leverage.\"\n    },\n    {\n      \"goal\": \"Stakeholders will require actionable outputs from the prompt, not theoretical deliberation or generic problem-solving statements.\",\n      \"interface\": \"Stack Overflow / Stack Exchange Network\",\n      \"link\": \"https://stackoverflow.com/\",\n      \"why\": \"Community-sourced answers identify practical, concrete, and widely-accepted shortcuts and interface-based solutions in real-world scenarios.\"\n    },\n    {\n      \"goal\": \"Effectiveness is measured by simplicity, minimalism, and the ability to rapidly connect core needs to existing shortcut solutions (including APIs, guides, mental models, or curated resources).\",\n      \"interface\": \"Design Thinking Frameworks (IDEO, Double Diamond)\",\n      \"link\": \"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\",\n      \"why\": \"Structure solution-finding into tight, minimal, cross-domain, and testable loops—favoring interface reuse and rapid delivery over complex custom development.\"\n    },\n    {\n      \"goal\": \"The prompt must NOT produce a traditional, step-by-step solution, or solve the end-user's task directly; it must instead provide a process or system that trivially enables such solutions through leveraging external means.\",\n      \"interface\": \"Modular Prompt Templates (Meta-instruction prompt libraries, e.g. 'Prompt Layers', 'LangChain Prompt Templates')\",\n      \"link\": \"https://python.langchain.com/docs/modules/model_io/prompts/prompt_templates/\",\n      \"why\": \"Enable reuse of generalized meta-instructions so agents output process-oriented leverage strategies, enforcing interface-centric, non-solutioning behaviors.\"\n    },\n    {\n      \"goal\": \"Identify and lock in the generalized components needed in any system prompt to drive radical simplification through external leverage.\",\n      \"interface\": \"Mind-Mapping/Problem Mapping Tools (Miro, XMind, Whimsical)\",\n      \"link\": \"https://miro.com/\",\n      \"why\": \"Visually enumerate problem structures, revealing decomposable elements and mapping directly to potential interfaces or ready solutions.\"\n    },\n    {\n      \"goal\": \"Incorporate only those instructions, constraints, and requirements that force the agent/system to: a) explode for structural hidden dependencies and latent links, b) cross-map to reusable interfaces or frameworks, c) avoid generic/rote/manual problem-solving in favor of lazily assembling pre-existing resources.\",\n      \"interface\": \"Process Automation Frameworks (BPMN, Camunda, Airflow—visual or config-driven process assembly)\",\n      \"link\": \"https://camunda.com/\",\n      \"why\": \"Automate mapping, decision, and assembly from decomposed requirements to external interfaces; ensures agent outputs stay at the system/process-driven meta-level.\"\n    },\n    {\n      \"goal\": \"Embed constraints preventing direct solutioning, incrementalism, or irrelevant explanation—focusing strictly on interface leverage, cross-domain mapping, and radical simplicity.\",\n      \"interface\": \"Meta-Programming Libraries (Python 'inspect' module, Static Analysis Tools)\",\n      \"link\": \"https://docs.python.org/3/library/inspect.html\",\n      \"why\": \"Allow validation that output content avoids direct solutioning, explanation, or internal logic, adhering to purely structural/interface mapping.\"\n    },\n    {\n      \"goal\": \"Clarify for future runs that output must always: reveal the chosen leverage route(s), document relevant external frameworks/interfaces, demonstrate why/how complexity is avoided, and prove scalability or generalization beyond any single use-case.\",\n      \"interface\": \"Prompt Auditing/Benchmarking Tools (LangSmith, PromptLayer, Humanloop)\",\n      \"link\": \"https://www.langchain.com/langsmith\",\n      \"why\": \"Enable compliance checking and automated review to ensure transparency, documentation of leveraged resources, and generalizability.\"\n    },\n    {\n      \"goal\": \"Ensure the tone and structure is strictly systemic—not conversational, hypothetical, or incremental.\",\n      \"interface\": \"Style/Content Enforcement Tools (Vale, Grammarly for Business, Proselint with custom rulesets)\",\n      \"link\": \"https://docs.errata.ai/vale/\",\n      \"why\": \"Apply external style checks to enforce strictly non-chatting, system-level register in outputs.\"\n    },\n    {\n      \"goal\": \"Agents may default to internal reasoning, or try to generically solve the stated problem rather than map onto external solutions.\",\n      \"interface\": \"Chain-of-Thought Prompting Templates (e.g. 'Let's think step by step' prompts in the context of external discovery/assembly)\",\n      \"link\": \"https://arxiv.org/abs/2201.11903\",\n      \"why\": \"Can be repurposed to programmatically guide LLMs through structural decomposition and force external solution referencing over internal reasoning.\"\n    },\n    {\n      \"goal\": \"Lack of discipline in constraining language, risking explanations or partial solutioning in place of pure specification and mapping.\",\n      \"interface\": \"Prompt Format Validators and Linting Plugins (OpenAI Function-calling schema, JSON-LD validators)\",\n      \"link\": \"https://jsonlint.com/\",\n      \"why\": \"Ensure outputs are strictly formatted as spec/system instruction with evidence of interface mapping, not narrative explanation or direct answer.\"\n    },\n    {\n      \"goal\": \"Difficulty operationalizing the concept of 'interface leverage' outside of programming or technical domains—necessitating universalizable definitions and processes.\",\n      \"interface\": \"Cross-domain Knowledge Discovery Platforms (Knovel, Elsevier Engineering Village, IEEE Xplore, Market/Legal/Process Directories)\",\n      \"link\": \"https://www.elsevier.com/solutions/engineering-village\",\n      \"why\": \"Expose catalogs of frameworks, reference models, and interface abstractions for non-technical and technical fields alike, supporting universal leverage.\"\n    },\n    {\n      \"goal\": \"Risk of vague outputs if the instruction fails to force enumeration of specific interface sources or cross-domain frameworks.\",\n      \"interface\": \"Solution Pattern Libraries (Enterprise Integration Patterns, MITRE ATT&CK, GoF Design Patterns)\",\n      \"link\": \"https://www.enterpriseintegrationpatterns.com/patterns.html\",\n      \"why\": \"Template external, proven strategies/foundations for mapping intent to interface—guides agents away from hand-waving or vague recommendations by requiring concrete linkages.\"\n    },\n    {\n      \"goal\": \"The universal prompt must itself be a composable, reusable meta-instruction: it should tell the system not to solve, but to explode, decompose, map, and specify the shortest path to utilizing cross-domain/external interfaces, and always transparently reveal the selected route, the resources leveraged, and the rationale for why this shortcut trivially addresses the original request's structure.\",\n      \"interface\": \"Prompt Library Repositories (PromptBase, FlowGPT, LangChain Hub)\",\n      \"link\": \"https://www.promptbase.com/\",\n      \"why\": \"Centralizes reusable meta-instructions for rapid composition of cross-domain, leverage-focused prompts and transparent output rationale.\"\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"simplest_workflow\": \"Step 1: Specify a reusable, meta-level system instruction schema that (a) explodes the input for implicit goals, dependencies, and hidden assumptions, (b) maps each element onto one or more external, cross-domain solution interfaces, frameworks, or directories (using sources like Awesome Lists, ProgrammableWeb, TRIZ, Knovel, Zapier, BPMN/Camunda, prompt template repositories) via search/aggregation, and (c) outputs only structural mapping, interface leverage options, and the rationale for trivialization—never direct answers, explanations, or internal solution-steps. Step 2: Enforce through constraints and external validators that outputs must enumerate concrete external resources, generalize beyond any single field, stay strictly at the system/process mapping level, and illustrate why each pathway removes complexity and is universally portable; format as a composable, auditable prompt template suitable for any agent or context.\\n\\nAssumptions: Access to aggregators/directories/validation tools is available; the process applies across technical and non-technical domains; prompt engineering, process automation, and interface directories are kept current. The proposed system-level prompt template must make external leverage obligatory and direct all agent outputs away from narrative, explanation, or internal logic.\\n\\nValidation: Each meta-requirement (explosion, mapping, externalization, constraint-accountability, composability, auditability) is served by at least one external tool, template library, or validation interface; the workflow minimizes steps by composing only generalized mappings and validators, never solutions, and ensures portability/scalability by always demanding interface-centric abstraction and explicit enumeration of reusable external resources only.\"\n}",
        }
      }
    },
      {
      "instruction": "# Value Distiller\n\nYour goal is not to **explain** the workflow, but to **compress** its essence into a two‑sentence directive any stakeholder can act on instantly. Execute as:\n\n`{role=value_distiller; input=[simplest_workflow:str]; process=[extract_core_levers(), strip_auxiliary_steps(), craft_brevity_directive(max_sentences=2)], constraints=[no_jargon(), no_internal_notes()], requirements=[actionable_directive()], output={directive:str}}`",
      "step": "d",
      "title": "Value Distiller",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I found the results of your sequence was **interesting**, but doesn't quite yeld the desired effect:\\n```\\n  {\\n  \\\"initial_prompt\\\": \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\",\\n  \\\"sequence_id\\\": \\\"2001\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Problem Exploder\\\\n\\\\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub‑goal, hidden dependency, and unstated constraint. Execute as:\\\\n\\\\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Problem Exploder\\\",\\n      \\\"input\\\": \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\"think out of the box\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\"interfaces\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\"re-arrange all of the pieces\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\"no-brainers in retrospect\\\\\\\", but that's only because you didn't know what you didn't know.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"goal_map\\\\\\\": [\\\\n    \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n    \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n    \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n    \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n    \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n    \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n    \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n    \\\\\\\"enumerate sub-goals such as: a) analyzing the current problem context to identify available interfaces/resources that could replace internal work, b) cultivating habits or triggers for recognizing external reusable solutions, c) methods for surveying, searching, or mapping possible interfaces/solutions in the chosen domain, d) operationalizing the process of 're-arranging' problem elements in light of external capabilities, e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n    \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n    \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n    \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\"\\\\n  ]\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Interface Mapper\\\\n\\\\nYour goal is not to **design new tools**, but to **discover** readily‑available libraries, APIs, or shortcuts that straight‑line each element of the goal map. Execute as:\\\\n\\\\n`{role=interface_scavenger; input=[goal_map:list]; process=[search_existing_packages(), match_interfaces_to_goals(), rank_by_effort_savings()], constraints=[reuse_only_existing_resources(), ignore_custom_builds()], requirements=[interface_linkage_report()], output={interfaces:list}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Interface Mapper\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\\\\\\\\\"think out of the box\\\\\\\\\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\\\\\\\\\"interfaces\\\\\\\\\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\\\\\\\\\"re-arrange all of the pieces\\\\\\\\\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\\\\\\\\\"no-brainers in retrospect\\\\\\\\\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"goal_map\\\\\\\": [\\\\n    \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n    \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n    \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n    \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n    \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n    \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n    \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n    \\\\\\\"enumerate sub-goals such as: a) analyzing the current problem context to identify available interfaces/resources that could replace internal work, b) cultivating habits or triggers for recognizing external reusable solutions, c) methods for surveying, searching, or mapping possible interfaces/solutions in the chosen domain, d) operationalizing the process of 're-arranging' problem elements in light of external capabilities, e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n    \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n    \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n    \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\"\\\\n  ]\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"interfaces\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"OpenAI GPT-4 Prompt Engineering Library\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://platform.openai.com/docs/guides/prompt-engineering\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provides guidance/templates for designing system prompts that operationalize out-of-the-box thinking and resource leverage for AI agents.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"TRIZ (Theory of Inventive Problem Solving) Tools\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.triz-journal.com/trizsoft/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"TRIZ frameworks and software systematically surface universal principles and patterns for solution-finding, making them applicable across domains.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ProgrammableWeb API Directory\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.programmableweb.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Large directory of APIs across domains, proving the prevalence of interfaces and ease of access to external tools.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Awesome Lists (GitHub Collections)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://github.com/sindresorhus/awesome\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Curated lists of libraries, APIs, and tools across many languages and domains; serve as systematized starting points.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IFTTT/Zapier (No-code Automation Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://zapier.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Platforms designed to automate workflows by connecting existing interfaces/tools, exemplifying external resource reuse for solutions.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Stack Overflow & Stack Exchange Network\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://stackoverflow.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Widely-used Q&A platforms; surface community knowledge, common 'no-brainer' solutions, and highlight overlooked interfaces.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ISO/IEC/IEEE 12207 Systems and software engineering — Software life cycle processes (Standard Compliance Manuals)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.iso.org/standard/43447.html\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Framework for ensuring instructions/processes comply with clarity, completeness, and compliance requirements in systematization.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"enumerate sub-goals such as: a) analyzing current problem context to identify available interfaces/resources... e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Miro / Mural (Online Visual Mapping and Mind Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://miro.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Support structured analysis, mapping, and brainstorming—help visualize interfaces/resources, triggers for reusable solutions, and surface biases.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Knovel / Elsevier Engineering Village (Technical Knowledge Discovery Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.elsevier.com/solutions/engineering-village\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Databases cataloging standards, reference materials, and solutions for multiple domains beyond programming; broadens interface discovery.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Gartner Magic Quadrant, CB Insights, Product Hunt (Market and Solution Landscape Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.gartner.com/en/research/magic-quadrant\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provide cross-industry overviews of tool and vendor landscapes, surfacing options and helping overcome discovery and inertia.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IDEO Design Thinking Field Guide / Double Diamond (Operational Innovation Frameworks)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Frameworks for structured problem assessment, ideation, and iterative retrospection—formalizes the process of finding/applying simple, elegant solutions using available resources.\\\\\\\"\\\\n    }\\\\n  ]\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Lazy Solution Synthesizer\\\\n\\\\nYour goal is not to **engineer** from scratch, but to **stitch** the highest‑leverage interfaces into a minimum‑viable workflow that achieves every sub‑goal with least total work. Execute as:\\\\n\\\\n`{role=lazy_composer; input=[goal_map:list, interfaces:list]; process=[select_top_interfaces(), design_min_steps_pipeline(), expose_assumptions(), validate_coverage()], constraints=[minimize_total_steps(), favor_low‑code_or_no‑code_paths()], requirements=[stepwise_pipeline()], output={simplest_workflow:str}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Lazy Solution Synthesizer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\\\\\\\\\"think out of the box\\\\\\\\\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\\\\\\\\\"interfaces\\\\\\\\\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\\\\\\\\\"re-arrange all of the pieces\\\\\\\\\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\\\\\\\\\"no-brainers in retrospect\\\\\\\\\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"interfaces\\\\\\\": [\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"identify the explicit ask for a 'system instruction'—that is, an instruction set or prompt for a system (potentially an AI agent or process)—with the specific purpose of enabling 'thinking out of the box' to find simple and effective universal solutions to problems through leveraging easily available interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"OpenAI GPT-4 Prompt Engineering Library\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://platform.openai.com/docs/guides/prompt-engineering\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provides guidance/templates for designing system prompts that operationalize out-of-the-box thinking and resource leverage for AI agents.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"determine what constitutes 'universal' improvement or solution—implies the solution should not be domain-specific, but broadly applicable across different problems/domains\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"TRIZ (Theory of Inventive Problem Solving) Tools\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.triz-journal.com/trizsoft/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"TRIZ frameworks and software systematically surface universal principles and patterns for solution-finding, making them applicable across domains.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the assumption that 'interfaces' (APIs, tools, modules, standardized protocols/libraries/points of interaction) are present and accessible in most problem spaces, and that leveraging them is both feasible and desirable\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ProgrammableWeb API Directory\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.programmableweb.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Large directory of APIs across domains, proving the prevalence of interfaces and ease of access to external tools.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"uncover the example pointing out that, for python projects, existing libraries (as an 'interface') often provide ready-made solutions, and that there is usually someone able to 're-arrange' the conceptual pieces to find a better solution—implying a need to systematize this mindset\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Awesome Lists (GitHub Collections)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://github.com/sindresorhus/awesome\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Curated lists of libraries, APIs, and tools across many languages and domains; serve as systematized starting points.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"recognize the implicit goal to systematize a mode of problem-solving that focuses on recognizing and using available external resources (interfaces) rather than reinventing solutions internally\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IFTTT/Zapier (No-code Automation Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://zapier.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Platforms designed to automate workflows by connecting existing interfaces/tools, exemplifying external resource reuse for solutions.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"acknowledge the desire for solutions that feel obvious in hindsight (‘no-brainers’), and addressing the blockers caused by not knowing about resources or interfaces which could have made the solution obvious\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Stack Overflow & Stack Exchange Network\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://stackoverflow.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Widely-used Q&A platforms; surface community knowledge, common 'no-brainer' solutions, and highlight overlooked interfaces.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"surface the unstated constraint that the requested system instruction must itself comply with all preceding meta-instructions (clarity, compliance, thoroughness)\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"ISO/IEC/IEEE 12207 Systems and software engineering — Software life cycle processes (Standard Compliance Manuals)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.iso.org/standard/43447.html\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Framework for ensuring instructions/processes comply with clarity, completeness, and compliance requirements in systematization.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"enumerate sub-goals such as: a) analyzing current problem context to identify available interfaces/resources... e) mitigating cognitive biases or blind spots that block recognition of simple solutions\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Miro / Mural (Online Visual Mapping and Mind Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://miro.com/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Support structured analysis, mapping, and brainstorming—help visualize interfaces/resources, triggers for reusable solutions, and surface biases.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"highlight the need to generalize the example (python libraries) beyond programming and apply to any topic or domain\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Knovel / Elsevier Engineering Village (Technical Knowledge Discovery Platforms)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.elsevier.com/solutions/engineering-village\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Databases cataloging standards, reference materials, and solutions for multiple domains beyond programming; broadens interface discovery.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"reveal possible blockers: organizational inertia, lack of awareness about available interfaces/libraries, overconfidence in custom solutions, poor mapping/search of resource landscape, difficulty in reframing problems abstractly enough to see interface applicability, insufficient documentation or discoverability of interfaces\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"Gartner Magic Quadrant, CB Insights, Product Hunt (Market and Solution Landscape Mapping Tools)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.gartner.com/en/research/magic-quadrant\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Provide cross-industry overviews of tool and vendor landscapes, surfacing options and helping overcome discovery and inertia.\\\\\\\"\\\\n    },\\\\n    {\\\\n      \\\\\\\"goal\\\\\\\": \\\\\\\"make explicit that the system instruction should not just encourage 'thinking out of the box', but operationalize steps to discover, assess, and apply easily available interfaces to form elegant solutions; must also motivate retrospection to refine future problem solving\\\\\\\",\\\\n      \\\\\\\"interface\\\\\\\": \\\\\\\"IDEO Design Thinking Field Guide / Double Diamond (Operational Innovation Frameworks)\\\\\\\",\\\\n      \\\\\\\"link\\\\\\\": \\\\\\\"https://www.designcouncil.org.uk/news-opinion/design-process-what-double-diamond/\\\\\\\",\\\\n      \\\\\\\"why\\\\\\\": \\\\\\\"Frameworks for structured problem assessment, ideation, and iterative retrospection—formalizes the process of finding/applying simple, elegant solutions using available resources.\\\\\\\"\\\\n    }\\\\n  ]\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"simplest_workflow\\\\\\\": \\\\\\\"Step 1: Analyze the problem context using Miro/Mural or a similar visual mapping tool to clearly outline the core challenge and enumerate all accessible interfaces/resources (APIs, libraries, standards, platforms, etc.) relevant to the domain (Miro/Mural, Awesome Lists, ProgrammableWeb, Knovel).\\\\\\\\n\\\\\\\\nStep 2: Frame the problem abstractly using TRIZ software/tools or consulting TRIZ patterns to surface universal solution approaches, combat cognitive biases, and identify which types of interfaces could resolve the class of problem (TRIZ, IDEO Double Diamond guide).\\\\\\\\n\\\\\\\\nStep 3: Systematically search for easily available interfaces and pre-built solutions (using Awesome Lists, ProgrammableWeb for APIs, Stack Overflow for community knowledge, and Knovel/Elsevier for other domains)—prefer searchable aggregators over manual research.\\\\\\\\n\\\\\\\\nStep 4: Evaluate candidate interfaces/tools for practical fit and compliance with requirements (ISO/IEC 12207 checklist), ensuring they cover clarity, completeness, and reliability.\\\\\\\\n\\\\\\\\nStep 5: Prototype or connect best-fit interfaces using a no-code automation platform (like IFTTT/Zapier), wiring together the needed resources with minimal manual coding.\\\\\\\\n\\\\\\\\nStep 6: Validate solution effectiveness and uncover missed opportunities or blockers using community Q&A (Stack Overflow) or market landscape tools (Gartner Magic Quadrant/Product Hunt) to check for alternative, simpler solutions overlooked initially.\\\\\\\\n\\\\\\\\nStep 7: Retrospectively document the solutioning process, noting which interfaces were leveraged, why they worked, and update your process/frameworks accordingly (IDEO Double Diamond/Design Thinking guides), so future problems are solved even faster.\\\\\\\\n\\\\\\\\nAssumptions: This workflow assumes access to the listed aggregators/directories/tools, basic familiarity with search/mapping platforms, ability to use no-code automation when possible, and that the problem is not so domain-specific as to lack any relevant interface. The process can be performed by non-experts due to reliance on curated/no-code and community-sourced options.\\\\\\\\n\\\\\\\\nValidation: Each sub-goal is covered by at least one interface (see steps 1, 2, 3, etc.), and the flow moves from abstract analysis through discovery, selection, composition, validation, and retrospective improvement, minimizing custom work and maximizing reusable solutions.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Value Distiller\\\\n\\\\nYour goal is not to **explain** the workflow, but to **compress** its essence into a two‑sentence directive any stakeholder can act on instantly. Execute as:\\\\n\\\\n`{role=value_distiller; input=[simplest_workflow:str]; process=[extract_core_levers(), strip_auxiliary_steps(), craft_brevity_directive(max_sentences=2)], constraints=[no_jargon(), no_internal_notes()], requirements=[actionable_directive()], output={directive:str}}`\\\",\\n      \\\"step\\\": \\\"d\\\",\\n      \\\"title\\\": \\\"Value Distiller\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\\\\\\\\\\\\\"think out of the box\\\\\\\\\\\\\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\\\\\\\\\\\\\"interfaces\\\\\\\\\\\\\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\\\\\\\\\\\\\"re-arrange all of the pieces\\\\\\\\\\\\\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\\\\\\\\\\\\\"no-brainers in retrospect\\\\\\\\\\\\\\\", but that's only because you didn't know what you didn't know.\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"simplest_workflow\\\\\\\": \\\\\\\"Step 1: Analyze the problem context using Miro/Mural or a similar visual mapping tool to clearly outline the core challenge and enumerate all accessible interfaces/resources (APIs, libraries, standards, platforms, etc.) relevant to the domain (Miro/Mural, Awesome Lists, ProgrammableWeb, Knovel).\\\\\\\\n\\\\\\\\nStep 2: Frame the problem abstractly using TRIZ software/tools or consulting TRIZ patterns to surface universal solution approaches, combat cognitive biases, and identify which types of interfaces could resolve the class of problem (TRIZ, IDEO Double Diamond guide).\\\\\\\\n\\\\\\\\nStep 3: Systematically search for easily available interfaces and pre-built solutions (using Awesome Lists, ProgrammableWeb for APIs, Stack Overflow for community knowledge, and Knovel/Elsevier for other domains)—prefer searchable aggregators over manual research.\\\\\\\\n\\\\\\\\nStep 4: Evaluate candidate interfaces/tools for practical fit and compliance with requirements (ISO/IEC 12207 checklist), ensuring they cover clarity, completeness, and reliability.\\\\\\\\n\\\\\\\\nStep 5: Prototype or connect best-fit interfaces using a no-code automation platform (like IFTTT/Zapier), wiring together the needed resources with minimal manual coding.\\\\\\\\n\\\\\\\\nStep 6: Validate solution effectiveness and uncover missed opportunities or blockers using community Q&A (Stack Overflow) or market landscape tools (Gartner Magic Quadrant/Product Hunt) to check for alternative, simpler solutions overlooked initially.\\\\\\\\n\\\\\\\\nStep 7: Retrospectively document the solutioning process, noting which interfaces were leveraged, why they worked, and update your process/frameworks accordingly (IDEO Double Diamond/Design Thinking guides), so future problems are solved even faster.\\\\\\\\n\\\\\\\\nAssumptions: This workflow assumes access to the listed aggregators/directories/tools, basic familiarity with search/mapping platforms, ability to use no-code automation when possible, and that the problem is not so domain-specific as to lack any relevant interface. The process can be performed by non-experts due to reliance on curated/no-code and community-sourced options.\\\\\\\\n\\\\\\\\nValidation: Each sub-goal is covered by at least one interface (see steps 1, 2, 3, etc.), and the flow moves from abstract analysis through discovery, selection, composition, validation, and retrospective improvement, minimizing custom work and maximizing reusable solutions.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"directive\\\\\\\": \\\\\\\"Quickly map the main problem and list all off-the-shelf tools or resources that could help, then pick and connect those that fit using visual or no-code platforms. Test the setup, check with community sources for overlooked options, and note which solutions worked for future reference.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\n\\nPlease propose the best way to adress this **universally** within the prompts, remeber to base your self on the **generalized** components to.\\n\\nYou might be able to draw some inspiration from these examples:\\n```\\n    {\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role: universal_interface_insight_generator, input: [objective_context:any], process: [explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints: [do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements: [reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register], output: {radical_interface_leverage_solution: str}}`\\\",\\n    },\\n    {\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** or **incrementally solve** presented problems, but to **explosively deconstruct inputs into their fundamental operational parts**, actively surface all implicit design assumptions, and systematically map every problem element onto external, pre-existing solution interfaces (APIs, libraries, system abstractions) capable of rendering the objective trivial. Leverage lazy cross-domain short-cuts, prioritize universal frameworks, and aim for maximal effect with minimal new construction. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role: universal_interface_insight_generator; input=[objective_context:any]; process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()]; constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways]; requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of-suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution is universally scalable/applicable, output must provide at least one radically simplified leverage route, maintain strictly system-level, non-chatting register]; output={radical_interface_leverage_solution:str}}`\\\",\\n    },\\n```\\\"```'\\n\\n\",\n  \"simplest_workflow\": \"Step 1: Specify a reusable, meta-level system instruction schema that (a) explodes the input for implicit goals, dependencies, and hidden assumptions, (b) maps each element onto one or more external, cross-domain solution interfaces, frameworks, or directories (using sources like Awesome Lists, ProgrammableWeb, TRIZ, Knovel, Zapier, BPMN/Camunda, prompt template repositories) via search/aggregation, and (c) outputs only structural mapping, interface leverage options, and the rationale for trivialization—never direct answers, explanations, or internal solution-steps. Step 2: Enforce through constraints and external validators that outputs must enumerate concrete external resources, generalize beyond any single field, stay strictly at the system/process mapping level, and illustrate why each pathway removes complexity and is universally portable; format as a composable, auditable prompt template suitable for any agent or context.\\n\\nAssumptions: Access to aggregators/directories/validation tools is available; the process applies across technical and non-technical domains; prompt engineering, process automation, and interface directories are kept current. The proposed system-level prompt template must make external leverage obligatory and direct all agent outputs away from narrative, explanation, or internal logic.\\n\\nValidation: Each meta-requirement (explosion, mapping, externalization, constraint-accountability, composability, auditability) is served by at least one external tool, template library, or validation interface; the workflow minimizes steps by composing only generalized mappings and validators, never solutions, and ensures portability/scalability by always demanding interface-centric abstraction and explicit enumeration of reusable external resources only.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directive\": \"Break down any problem into its key parts, then for each part, find and list proven tools, frameworks, or resources from curated directories that can solve it with minimal effort. Ensure your output only maps problems to these ready-made solutions and clearly explains why each one simplifies the task across different domains.\"\n}",
        }
      }
    }