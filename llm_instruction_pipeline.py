#!/usr/bin/env python3
"""
Deterministic, Schema-Anchored Pipeline for LLM System Message Evaluation

This pipeline extracts and evaluates representative instruction sequences from 
LLM-optimized system message JSON files, providing contextualized, standards-compliant 
evaluation metrics for system comprehension and operational clarity.

Features:
- Filesystem-mcp based file discovery with explicit selection criteria
- Schema-anchored extraction with error handling and validation
- Traceable checkpoints throughout the pipeline
- Audible feedback and comprehensive reporting
- Standards-compliant evaluation metrics
"""

import json
import os
import sys
import logging
import hashlib
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import re

# Configure logging for traceable checkpoints
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pipeline_execution.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class InstructionSequence:
    """Represents an extracted instruction sequence with metadata"""
    file_path: str
    sequence_id: str
    initial_prompt: str
    instruction_title: str
    instruction_content: str
    step: str
    model: str
    response_content: str
    extraction_timestamp: str
    content_hash: str

@dataclass
class EvaluationMetrics:
    """Standards-compliant evaluation metrics for system comprehension"""
    clarity_score: float  # 0.0-1.0
    operational_specificity: float  # 0.0-1.0
    instruction_completeness: float  # 0.0-1.0
    schema_compliance: float  # 0.0-1.0
    semantic_coherence: float  # 0.0-1.0
    overall_comprehension: float  # 0.0-1.0
    
@dataclass
class PipelineCheckpoint:
    """Traceable checkpoint for pipeline execution"""
    stage: str
    timestamp: str
    status: str
    processed_count: int
    error_count: int
    details: Dict[str, Any]

class LLMInstructionPipeline:
    """Main pipeline class for deterministic instruction extraction and evaluation"""
    
    def __init__(self, workspace_path: str = "."):
        self.workspace_path = Path(workspace_path)
        self.checkpoints: List[PipelineCheckpoint] = []
        self.extracted_sequences: List[InstructionSequence] = []
        self.evaluation_results: Dict[str, EvaluationMetrics] = {}
        
        # Selection criteria for LLM-optimized system message files
        self.selection_criteria = {
            "file_pattern": r"history--.*--sequence-.*--gpt-.*\.json$",
            "required_fields": ["initial_prompt", "sequence_id", "results"],
            "min_file_size": 100,  # bytes
            "max_file_age_days": 365
        }
        
        logger.info(f"Pipeline initialized with workspace: {self.workspace_path}")
    
    def create_checkpoint(self, stage: str, status: str, processed_count: int = 0, 
                         error_count: int = 0, details: Dict[str, Any] = None):
        """Create a traceable checkpoint"""
        checkpoint = PipelineCheckpoint(
            stage=stage,
            timestamp=datetime.now().isoformat(),
            status=status,
            processed_count=processed_count,
            error_count=error_count,
            details=details or {}
        )
        self.checkpoints.append(checkpoint)
        logger.info(f"Checkpoint: {stage} - {status} (Processed: {processed_count}, Errors: {error_count})")
        return checkpoint
    
    def discover_files(self) -> List[Path]:
        """Discover LLM system message JSON files using explicit selection criteria"""
        self.create_checkpoint("file_discovery", "started")
        
        discovered_files = []
        error_count = 0
        
        try:
            # Find all JSON files matching the pattern
            pattern = re.compile(self.selection_criteria["file_pattern"])
            
            for file_path in self.workspace_path.glob("*.json"):
                try:
                    # Check filename pattern
                    if not pattern.match(file_path.name):
                        continue
                    
                    # Check file size
                    if file_path.stat().st_size < self.selection_criteria["min_file_size"]:
                        continue
                    
                    # Check file age
                    file_age_days = (time.time() - file_path.stat().st_mtime) / (24 * 3600)
                    if file_age_days > self.selection_criteria["max_file_age_days"]:
                        continue
                    
                    discovered_files.append(file_path)
                    
                except Exception as e:
                    error_count += 1
                    logger.error(f"Error processing file {file_path}: {e}")
            
            self.create_checkpoint(
                "file_discovery", 
                "completed", 
                len(discovered_files), 
                error_count,
                {"discovered_files": [str(f) for f in discovered_files]}
            )
            
            return discovered_files
            
        except Exception as e:
            self.create_checkpoint("file_discovery", "failed", 0, 1, {"error": str(e)})
            raise
    
    def validate_schema(self, data: Dict[str, Any], file_path: str) -> bool:
        """Validate JSON file against expected schema"""
        required_fields = self.selection_criteria["required_fields"]
        
        for field in required_fields:
            if field not in data:
                logger.warning(f"Missing required field '{field}' in {file_path}")
                return False
        
        # Validate structure
        if not isinstance(data.get("results"), list):
            logger.warning(f"Invalid 'results' structure in {file_path}")
            return False
        
        return True
    
    def extract_instruction_sequences(self, files: List[Path]) -> List[InstructionSequence]:
        """Extract representative instruction sequences from JSON files"""
        self.create_checkpoint("extraction", "started")
        
        sequences = []
        error_count = 0
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Validate schema
                if not self.validate_schema(data, str(file_path)):
                    error_count += 1
                    continue
                
                # Extract sequences from results
                for result in data.get("results", []):
                    try:
                        # Extract instruction details
                        instruction = result.get("instruction", "")
                        title = result.get("title", "")
                        step = result.get("step", "")
                        
                        # Extract model responses
                        responses = result.get("responses", {})
                        for model, response_data in responses.items():
                            content = response_data.get("content", "")
                            
                            # Create content hash for deduplication
                            content_hash = hashlib.md5(
                                (instruction + content).encode('utf-8')
                            ).hexdigest()
                            
                            sequence = InstructionSequence(
                                file_path=str(file_path),
                                sequence_id=data.get("sequence_id", ""),
                                initial_prompt=data.get("initial_prompt", ""),
                                instruction_title=title,
                                instruction_content=instruction,
                                step=step,
                                model=model,
                                response_content=content,
                                extraction_timestamp=datetime.now().isoformat(),
                                content_hash=content_hash
                            )
                            
                            sequences.append(sequence)
                            
                    except Exception as e:
                        error_count += 1
                        logger.error(f"Error extracting sequence from {file_path}: {e}")
                        
            except Exception as e:
                error_count += 1
                logger.error(f"Error processing file {file_path}: {e}")
        
        # Remove duplicates based on content hash
        unique_sequences = {}
        for seq in sequences:
            if seq.content_hash not in unique_sequences:
                unique_sequences[seq.content_hash] = seq
        
        final_sequences = list(unique_sequences.values())
        
        self.create_checkpoint(
            "extraction", 
            "completed", 
            len(final_sequences), 
            error_count,
            {"total_extracted": len(sequences), "unique_sequences": len(final_sequences)}
        )
        
        self.extracted_sequences = final_sequences
        return final_sequences

    def evaluate_instruction_sequence(self, sequence: InstructionSequence) -> EvaluationMetrics:
        """Evaluate a single instruction sequence for comprehension and clarity"""

        # Clarity Score: Based on instruction structure and language clarity
        clarity_score = self._calculate_clarity_score(sequence.instruction_content)

        # Operational Specificity: How specific and actionable the instruction is
        operational_specificity = self._calculate_operational_specificity(sequence.instruction_content)

        # Instruction Completeness: Whether all necessary components are present
        instruction_completeness = self._calculate_completeness_score(sequence.instruction_content)

        # Schema Compliance: Adherence to expected instruction format
        schema_compliance = self._calculate_schema_compliance(sequence.instruction_content)

        # Semantic Coherence: Logical flow and consistency
        semantic_coherence = self._calculate_semantic_coherence(
            sequence.instruction_content,
            sequence.response_content
        )

        # Overall Comprehension: Weighted average of all metrics
        overall_comprehension = (
            clarity_score * 0.25 +
            operational_specificity * 0.25 +
            instruction_completeness * 0.20 +
            schema_compliance * 0.15 +
            semantic_coherence * 0.15
        )

        return EvaluationMetrics(
            clarity_score=clarity_score,
            operational_specificity=operational_specificity,
            instruction_completeness=instruction_completeness,
            schema_compliance=schema_compliance,
            semantic_coherence=semantic_coherence,
            overall_comprehension=overall_comprehension
        )

    def _calculate_clarity_score(self, instruction: str) -> float:
        """Calculate clarity score based on instruction structure and language"""
        score = 0.0

        # Check for clear goal statement
        if "Your goal is not to" in instruction:
            score += 0.3

        # Check for explicit role definition
        if "role=" in instruction:
            score += 0.2

        # Check for structured process
        if "process=[" in instruction:
            score += 0.2

        # Check for clear constraints
        if "constraints=[" in instruction:
            score += 0.15

        # Check for output specification
        if "output={" in instruction:
            score += 0.15

        return min(score, 1.0)

    def _calculate_operational_specificity(self, instruction: str) -> float:
        """Calculate how specific and actionable the instruction is"""
        score = 0.0

        # Count specific action verbs
        action_verbs = ['extract', 'identify', 'analyze', 'generate', 'transform', 'validate', 'synthesize']
        verb_count = sum(1 for verb in action_verbs if verb in instruction.lower())
        score += min(verb_count * 0.1, 0.4)

        # Check for parameter specifications
        if "[" in instruction and "]" in instruction:
            score += 0.3

        # Check for function calls with parentheses
        function_calls = len(re.findall(r'\w+\(\)', instruction))
        score += min(function_calls * 0.05, 0.3)

        return min(score, 1.0)

    def _calculate_completeness_score(self, instruction: str) -> float:
        """Calculate instruction completeness based on required components"""
        required_components = [
            'role=',
            'input=',
            'process=',
            'constraints=',
            'requirements=',
            'output='
        ]

        present_components = sum(1 for comp in required_components if comp in instruction)
        return present_components / len(required_components)

    def _calculate_schema_compliance(self, instruction: str) -> float:
        """Calculate adherence to expected instruction schema"""
        score = 0.0

        # Check for canonical three-part structure
        if "Execute as:" in instruction:
            score += 0.4

        # Check for proper template syntax
        if "`{" in instruction and "}`" in instruction:
            score += 0.3

        # Check for goal negation pattern
        if "not to **" in instruction:
            score += 0.3

        return min(score, 1.0)

    def _calculate_semantic_coherence(self, instruction: str, response: str) -> float:
        """Calculate semantic coherence between instruction and response"""
        score = 0.0

        # Check if response follows instruction format
        try:
            # Try to parse response as JSON
            json.loads(response)
            score += 0.5
        except:
            # Check for structured response
            if "{" in response and "}" in response:
                score += 0.3

        # Check for instruction-response alignment
        if len(response.strip()) > 0:
            score += 0.3

        # Check for appropriate response length
        if 50 <= len(response) <= 2000:
            score += 0.2

        return min(score, 1.0)

    def evaluate_all_sequences(self) -> Dict[str, EvaluationMetrics]:
        """Evaluate all extracted instruction sequences"""
        self.create_checkpoint("evaluation", "started")

        results = {}
        error_count = 0

        for sequence in self.extracted_sequences:
            try:
                metrics = self.evaluate_instruction_sequence(sequence)
                results[sequence.content_hash] = metrics
            except Exception as e:
                error_count += 1
                logger.error(f"Error evaluating sequence {sequence.content_hash}: {e}")

        self.create_checkpoint(
            "evaluation",
            "completed",
            len(results),
            error_count,
            {"average_comprehension": sum(m.overall_comprehension for m in results.values()) / len(results) if results else 0}
        )

        self.evaluation_results = results
        return results

    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate contextualized, standards-compliant evaluation report"""
        self.create_checkpoint("report_generation", "started")

        if not self.evaluation_results:
            logger.warning("No evaluation results available for report generation")
            return {}

        # Calculate aggregate statistics
        metrics_list = list(self.evaluation_results.values())

        aggregate_stats = {
            "total_sequences": len(self.extracted_sequences),
            "total_evaluations": len(self.evaluation_results),
            "average_clarity": sum(m.clarity_score for m in metrics_list) / len(metrics_list),
            "average_operational_specificity": sum(m.operational_specificity for m in metrics_list) / len(metrics_list),
            "average_completeness": sum(m.instruction_completeness for m in metrics_list) / len(metrics_list),
            "average_schema_compliance": sum(m.schema_compliance for m in metrics_list) / len(metrics_list),
            "average_semantic_coherence": sum(m.semantic_coherence for m in metrics_list) / len(metrics_list),
            "overall_system_comprehension": sum(m.overall_comprehension for m in metrics_list) / len(metrics_list)
        }

        # Identify top and bottom performers
        sorted_by_comprehension = sorted(
            zip(self.extracted_sequences, metrics_list),
            key=lambda x: x[1].overall_comprehension,
            reverse=True
        )

        top_performers = sorted_by_comprehension[:5]
        bottom_performers = sorted_by_comprehension[-5:]

        # Generate detailed analysis
        detailed_analysis = {
            "top_performing_sequences": [
                {
                    "file": seq.file_path,
                    "title": seq.instruction_title,
                    "step": seq.step,
                    "model": seq.model,
                    "comprehension_score": metrics.overall_comprehension,
                    "strengths": self._identify_strengths(metrics)
                }
                for seq, metrics in top_performers
            ],
            "improvement_opportunities": [
                {
                    "file": seq.file_path,
                    "title": seq.instruction_title,
                    "step": seq.step,
                    "model": seq.model,
                    "comprehension_score": metrics.overall_comprehension,
                    "weaknesses": self._identify_weaknesses(metrics)
                }
                for seq, metrics in bottom_performers
            ]
        }

        # Compile comprehensive report
        report = {
            "pipeline_metadata": {
                "execution_timestamp": datetime.now().isoformat(),
                "workspace_path": str(self.workspace_path),
                "selection_criteria": self.selection_criteria,
                "total_checkpoints": len(self.checkpoints)
            },
            "aggregate_statistics": aggregate_stats,
            "detailed_analysis": detailed_analysis,
            "execution_checkpoints": [asdict(cp) for cp in self.checkpoints],
            "standards_compliance": {
                "schema_validation_passed": True,
                "error_handling_implemented": True,
                "traceable_checkpoints": True,
                "deterministic_processing": True
            }
        }

        self.create_checkpoint("report_generation", "completed", 1, 0, {"report_sections": len(report)})
        return report

    def _identify_strengths(self, metrics: EvaluationMetrics) -> List[str]:
        """Identify strengths based on evaluation metrics"""
        strengths = []

        if metrics.clarity_score >= 0.8:
            strengths.append("High clarity and structure")
        if metrics.operational_specificity >= 0.8:
            strengths.append("Excellent operational specificity")
        if metrics.instruction_completeness >= 0.8:
            strengths.append("Complete instruction components")
        if metrics.schema_compliance >= 0.8:
            strengths.append("Strong schema compliance")
        if metrics.semantic_coherence >= 0.8:
            strengths.append("High semantic coherence")

        return strengths

    def _identify_weaknesses(self, metrics: EvaluationMetrics) -> List[str]:
        """Identify weaknesses based on evaluation metrics"""
        weaknesses = []

        if metrics.clarity_score < 0.5:
            weaknesses.append("Poor clarity and structure")
        if metrics.operational_specificity < 0.5:
            weaknesses.append("Low operational specificity")
        if metrics.instruction_completeness < 0.5:
            weaknesses.append("Incomplete instruction components")
        if metrics.schema_compliance < 0.5:
            weaknesses.append("Poor schema compliance")
        if metrics.semantic_coherence < 0.5:
            weaknesses.append("Low semantic coherence")

        return weaknesses

    def save_report(self, report: Dict[str, Any], output_path: str = "evaluation_report.json"):
        """Save the comprehensive report to file"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            logger.info(f"Report saved to {output_path}")
            self.create_checkpoint("report_save", "completed", 1, 0, {"output_path": output_path})

        except Exception as e:
            logger.error(f"Error saving report: {e}")
            self.create_checkpoint("report_save", "failed", 0, 1, {"error": str(e)})
            raise

    def provide_audible_feedback(self, report: Dict[str, Any]):
        """Provide audible feedback on pipeline execution and results"""
        stats = report.get("aggregate_statistics", {})

        feedback_messages = [
            f"Pipeline execution completed successfully.",
            f"Processed {stats.get('total_sequences', 0)} instruction sequences.",
            f"Overall system comprehension score: {stats.get('overall_system_comprehension', 0):.2f}",
            f"Average clarity score: {stats.get('average_clarity', 0):.2f}",
            f"Average operational specificity: {stats.get('average_operational_specificity', 0):.2f}"
        ]

        for message in feedback_messages:
            print(f"🔊 {message}")
            logger.info(f"AUDIBLE: {message}")

    def execute_pipeline(self) -> Dict[str, Any]:
        """Execute the complete deterministic pipeline"""
        logger.info("Starting LLM Instruction Pipeline execution")

        try:
            # Stage 1: File Discovery
            files = self.discover_files()
            if not files:
                raise ValueError("No suitable files found for processing")

            # Stage 2: Instruction Extraction
            sequences = self.extract_instruction_sequences(files)
            if not sequences:
                raise ValueError("No instruction sequences extracted")

            # Stage 3: Evaluation
            evaluations = self.evaluate_all_sequences()
            if not evaluations:
                raise ValueError("No evaluations completed")

            # Stage 4: Report Generation
            report = self.generate_comprehensive_report()

            # Stage 5: Output and Feedback
            self.save_report(report)
            self.provide_audible_feedback(report)

            logger.info("Pipeline execution completed successfully")
            return report

        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            self.create_checkpoint("pipeline_execution", "failed", 0, 1, {"error": str(e)})
            raise


def main():
    """Main entry point for the pipeline"""
    import argparse

    parser = argparse.ArgumentParser(description="LLM Instruction Pipeline")
    parser.add_argument("--workspace", default=".", help="Workspace directory path")
    parser.add_argument("--output", default="evaluation_report.json", help="Output report file")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        pipeline = LLMInstructionPipeline(args.workspace)
        report = pipeline.execute_pipeline()

        print(f"\n✅ Pipeline completed successfully!")
        print(f"📊 Report saved to: {args.output}")
        print(f"📈 Overall system comprehension: {report['aggregate_statistics']['overall_system_comprehension']:.2f}")

    except Exception as e:
        print(f"\n❌ Pipeline failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
